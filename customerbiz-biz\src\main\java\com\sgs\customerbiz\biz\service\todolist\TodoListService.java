package com.sgs.customerbiz.biz.service.todolist;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.service.ConfigService;
import com.sgs.config.api.service.SystemAPIConfigService;
import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.utils.FastJSONUtils;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.config.TodoListParamConfig;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.util.*;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.BoundTrfRelExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfTodoInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.SearchTrfInfoDTO;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.CfgTodoListRefSystemIdMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.ConfigInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfLogMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.cache.CustomerSceneCacheService;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.enums.ChangeTypeEnum;
import com.sgs.customerbiz.domain.enums.TrfReportLevelEnum;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.dto.SampleFormDTO;
import com.sgs.customerbiz.facade.model.dto.TodoListBaseDataDTO;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.facade.model.enums.CreateType;
import com.sgs.customerbiz.facade.model.enums.SearchType;
import com.sgs.customerbiz.facade.model.enums.SendTrfMainInfoType;
import com.sgs.customerbiz.facade.model.file.FileInfo;
import com.sgs.customerbiz.facade.model.req.ConvertDataReq;
import com.sgs.customerbiz.facade.model.req.RenderTrfDetailToFileReq;
import com.sgs.customerbiz.facade.model.rsp.trforder.SceneTypeInfo;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfContentDTO;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfTodoDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.GetTrfDetailPrintRsp;
import com.sgs.customerbiz.facade.model.trf.req.BoundTrfInfoSearchReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CheckCustomerRuleRsp;
import com.sgs.customerbiz.facade.model.trf.rsp.CreateOrderSampleRuleItem;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerRuleItem;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.*;
import com.sgs.customerbiz.integration.dto.DigitalReportRenderToFileRsp;
import com.sgs.customerbiz.integration.enums.FileNetworkType;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.GetCustomerConfigReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.tool.info.ExcelCommonInfo;
import com.sgs.framework.tool.info.ExcelDataConfigInfo;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.ExcelUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.sgs.customerbiz.core.constants.Constants.*;
import javax.annotation.PostConstruct;

@Service
public class TodoListService {
    private static final Logger logger = LoggerFactory.getLogger(TodoListService.class);
    @Autowired
    private LocalILayerClient iLayerClient;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private CustomerSceneCacheService customerSceneCacheService;
    @Resource
    private BoundTrfRelExtMapper boundTrfRelExtMapper;
    @Resource
    private TrfInfoExtMapper trfInfoExtMapper;
    @Resource
    private TrfInfoMapper trfInfoMapper;
    @Autowired
    private CustomerConfigClient customerConfigClient;
    @Resource
    private TrfTodoInfoExtMapper trfTodoInfoExtMapper;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private SciTrfBizService sciTrfBizService;

    @Autowired
    private TrfService trfService;

    @Resource
    private DigitalReportClient digitalReportClient;

    @Resource
    private TokenUtils tokenUtils;

    @Autowired
    private TrfDomainService trfDomainService;
    @Autowired
    private TrfOrderDomainService trfOrderDomainService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TrfLogMapper trfLogMapper;
    @Resource
    private IdService idService;
    @Autowired
    private JsonDataConvertor jsonDataConvertor;

    @Autowired
    private ConfigClient configClient;

    @Autowired
    private SystemAPIConfigService systemAPIConfigService;

    @Autowired
    private TodoListParamConfig todoListParamConfig;

    @Autowired
    private CfgTodoListRefSystemIdMapper todoListRefSystemIdMapper;

    @Autowired
    private ConfigInfoMapper configInfoMapper;

    /**
     * 初始化系统ID配置
     */
    @PostConstruct
    public void initRefSystemIdConfig() {
        logger.info("初始化系统ID配置...");
        // 如果配置为空，则使用默认配置
        if (todoListParamConfig.getRefSystemIdConfigItems() == null || todoListParamConfig.getRefSystemIdConfigItems().isEmpty()) {
            logger.warn("系统ID配置为空，请检查配置文件");
        } else {
            logger.info("系统ID配置初始化完成，共{}项", todoListParamConfig.getRefSystemIdConfigItems().size());
        }
    }

    private static final int CORE_POOL_SIZE = 3;
    private static final int MAX_POOL_SIZE = 5;
    private static final int QUEUE_CAPACITY = 10;
    private static final Long KEEP_ALIVE_TIME = 10L;


    public static final List<RefSystemIdEnum> refSystemIdList = new ArrayList<>();

    static {
        refSystemIdList.add(RefSystemIdEnum.TIC);
        refSystemIdList.add(RefSystemIdEnum.Shein);
        refSystemIdList.add(RefSystemIdEnum.SGSMart);
        refSystemIdList.add(RefSystemIdEnum.Septwolves);
        refSystemIdList.add(RefSystemIdEnum.F21);
        refSystemIdList.add(RefSystemIdEnum.JO_ANN);
        refSystemIdList.add(RefSystemIdEnum.Target);
        refSystemIdList.add(RefSystemIdEnum.BigLots);
        refSystemIdList.add(RefSystemIdEnum.Walmart);
        refSystemIdList.add(RefSystemIdEnum.Walmart_Group);
        refSystemIdList.add(RefSystemIdEnum.Veyer);
        refSystemIdList.add(RefSystemIdEnum.DollarTree);
    }

    @Data
    @AllArgsConstructor
    public static class RefSystemIdValue {
        private int refSystemId;
        private String customerGroupCode;
    }

    @Data
    @AllArgsConstructor
    public static class RefSystemIdConfig {
        private Integer id;
        private String label;
        private boolean disabled;
        private RefSystemIdValue value;
        private List<RefSystemIdConfig> children;
        private Integer seqNumber;

        public static RefSystemIdConfig of(int refSystemId, String label, String customerGroupCode) {
            return of(0, refSystemId, label, false, customerGroupCode, null, 1);
        }

        public static RefSystemIdConfig of(int refSystemId, String label, boolean disabled, String customerGroupCode, List<RefSystemIdConfig> children) {
            return of(0, refSystemId, label, disabled, customerGroupCode, children, 1);
        }

        public static RefSystemIdConfig of(int refSystemId, String label, boolean disabled, String customerGroupCode, List<RefSystemIdConfig> children, Integer seqNumber) {
            return of(0, refSystemId, label, disabled, customerGroupCode, children, seqNumber);
        }

        public static RefSystemIdConfig of(Integer id, int refSystemId, String label, boolean disabled, String customerGroupCode, List<RefSystemIdConfig> children, Integer seqNumber) {
            return new RefSystemIdConfig(id, label, disabled, new RefSystemIdValue(refSystemId, customerGroupCode), children, seqNumber);
        }
    }

    public CustomResult refSystemId(QueryRefSystemId queryRefSystemId) {
        CustomResult<Object> result = CustomResult.newSuccessInstance();

        // 1. 根据keyOfPage查询所有的CfgTodoListRefSystemId
        CfgTodoListRefSystemIdExample example = new CfgTodoListRefSystemIdExample();
        example.createCriteria().andKeyOfPageEqualTo(queryRefSystemId.getPage());
        List<CfgTodoListRefSystemIdPO> refSystemIdList = todoListRefSystemIdMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(refSystemIdList)) {
            result.setData(Collections.emptyList());
            return result;
        }

        // 2. 收集查询结果集的refSystemId
        List<Integer> refSystemIds = refSystemIdList.stream()
                .map(CfgTodoListRefSystemIdPO::getRefSystemId)
                .distinct()
                .collect(Collectors.toList());

        // 3. 通过refSystemId + configKey='CustomerConfig' 查询所有的ConfigInfo
        ConfigInfoExample configInfoExample = new ConfigInfoExample();
        configInfoExample.createCriteria()
                .andIdentityIdIn(refSystemIds.stream().map(String::valueOf).collect(Collectors.toList()))
                .andConfigKeyEqualTo("CustomerConfig");
        List<ConfigInfoPO> configInfoList = configInfoMapper.selectByExample(configInfoExample);

        // 4. 将ConfigInfo的configValue转换成CustomerGeneralConfig对象
        Map<Integer, CustomerGeneralConfig> refSystemId2ConfigMap = new HashMap<>();
        for (ConfigInfoPO configInfo : configInfoList) {
            try {
                CustomerGeneralConfig config = JSON.parseObject(configInfo.getConfigValue(), CustomerGeneralConfig.class);
                if (config != null && config.getIntegrationSystemRefSystemId() != null) {
                    refSystemId2ConfigMap.put(config.getIntegrationSystemRefSystemId(), config);
                }
            } catch (Exception e) {
                logger.error("Parse CustomerGeneralConfig error", e);
            }
        }

        // 5. 构建树形结构
        List<RefSystemIdConfig> resultList = buildRefSystemIdConfigTree(refSystemIdList, refSystemId2ConfigMap);

        // 6. 对结果列表进行排序（先按seqNumber升序，再按id升序）
        sortRefSystemIdConfigList(resultList);

        result.setData(resultList);
        return result;
    }

    /**
     * 对RefSystemIdConfig列表进行排序，包括子列表
     * @param configList 要排序的RefSystemIdConfig列表
     */
    private void sortRefSystemIdConfigList(List<RefSystemIdConfig> configList) {
        if (configList == null || configList.isEmpty()) {
            return;
        }

        // 对当前层级的列表进行排序（先按seqNumber升序，再按id升序）
        configList.sort(Comparator.<RefSystemIdConfig, Integer>comparing(
                config -> config.getSeqNumber() != null ? config.getSeqNumber() : Integer.MAX_VALUE)
                .thenComparing(config -> config.getId() != null ? config.getId() : Integer.MAX_VALUE)
                .thenComparing(config -> config.getValue().getRefSystemId()));

        // 递归对子列表进行排序
        for (RefSystemIdConfig config : configList) {
            if (config.getChildren() != null && !config.getChildren().isEmpty()) {
                sortRefSystemIdConfigList(config.getChildren());
            }
        }
    }

    /**
     * 构建RefSystemIdConfig树形结构
     * @param refSystemIdList CfgTodoListRefSystemId列表
     * @param refSystemId2ConfigMap refSystemId到CustomerGeneralConfig的映射
     * @return RefSystemIdConfig树形列表
     */
    private List<RefSystemIdConfig> buildRefSystemIdConfigTree(List<CfgTodoListRefSystemIdPO> refSystemIdList,
                                                              Map<Integer, CustomerGeneralConfig> refSystemId2ConfigMap) {
        // 按parentId分组
        Map<Integer, List<CfgTodoListRefSystemIdPO>> parentIdMap = refSystemIdList.stream()
                .collect(Collectors.groupingBy(CfgTodoListRefSystemIdPO::getParentId));

        // 获取根节点列表（parentId为0的节点）
        List<CfgTodoListRefSystemIdPO> rootNodes = parentIdMap.getOrDefault(0, Collections.emptyList());

        // 递归构建树
        return rootNodes.stream()
                .map(node -> convertToRefSystemIdConfig(node, parentIdMap, refSystemId2ConfigMap))
                .collect(Collectors.toList());
    }

    /**
     * 将CfgTodoListRefSystemIdPO转换为RefSystemIdConfig，并递归构建子节点
     */
    private RefSystemIdConfig convertToRefSystemIdConfig(CfgTodoListRefSystemIdPO node,
                                                         Map<Integer, List<CfgTodoListRefSystemIdPO>> parentIdMap,
                                                         Map<Integer, CustomerGeneralConfig> refSystemId2ConfigMap) {
        Integer refSystemId = node.getRefSystemId();
        CustomerGeneralConfig config = refSystemId2ConfigMap.get(refSystemId);

        // 获取customerGroupCode
        String customerGroupCode = "";
        String label = "";

        if (config != null) {
            customerGroupCode = config.getCustomerGroupCode() != null ? config.getCustomerGroupCode() : "";
            // 优先使用英文名称，如果没有则使用集成系统名称
            label = StringUtils.isNotBlank(config.getCustomerNameEn()) ?
                    config.getCustomerNameEn() :
                    (StringUtils.isNotBlank(config.getIntegrationSystemName()) ?
                            config.getIntegrationSystemName() :
                            String.valueOf(refSystemId));
        } else {
            // 如果没有找到对应的配置，使用refSystemId作为label
            label = Optional.ofNullable(todoListParamConfig.getRefSystemIdConfigItemMap().get(refSystemId)).map(TodoListParamConfig.RefSystemIdConfigItem::getLabel).orElse(String.valueOf(refSystemId));
            customerGroupCode = Optional.ofNullable(todoListParamConfig.getRefSystemIdConfigItemMap().get(refSystemId)).map(TodoListParamConfig.RefSystemIdConfigItem::getCustomerGroupCode).orElse("");
        }

        // 检查是否有子节点
        List<CfgTodoListRefSystemIdPO> children = parentIdMap.getOrDefault(node.getId(), Collections.emptyList());

        // 获取排序号和ID
        Integer seqNumber = node.getSeqNumber() != null ? node.getSeqNumber() : 1;
        Integer id = node.getId();

        if (CollectionUtils.isEmpty(children)) {
            // 没有子节点
            boolean disabled = node.getDisabled() != null && node.getDisabled() == 1;
            return RefSystemIdConfig.of(id, refSystemId, label, disabled, customerGroupCode, null, seqNumber);
        } else {
            // 有子节点，递归构建
            List<RefSystemIdConfig> childConfigs = children.stream()
                    .map(child -> convertToRefSystemIdConfig(child, parentIdMap, refSystemId2ConfigMap))
                    .collect(Collectors.toList());

            boolean disabled = node.getDisabled() != null && node.getDisabled() == 1;
            return RefSystemIdConfig.of(id, refSystemId, label, disabled, customerGroupCode, childConfigs, seqNumber);
        }
    }

    /**
     * @param reqObject
     * @return
     * @NotNull注解校验 productLineCode、customerGroupCode 必传
     */
    public CustomResult trfTodoList(TrfTodoReq reqObject) {
        // TODO 临时处理 SCI-900
        Integer refSystemId = reqObject.getRefSystemId();
        if (refSystemId == 2) {
            reqObject.setRefSystemId(1);
        }
        CustomResult<Object> result = new CustomResult<>();

        ProductLineType productLineAbbr = ProductLineType.findProductLineAbbr(ProductLineContextHolder.getProductLineCode());
        if (productLineAbbr == null) {
            return result.fail("Get productLineCode Fail!");
        }
        int productLineId = productLineAbbr.getProductLineId();

        ConcurrentHashMap<String, JsonNode> customerSceneList = customerSceneCacheService.getCustomerSceneList(reqObject, RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO) ? 0 : productLineId);
        result.setSuccess(true);
        result.setData(customerSceneList);
        return result;
    }

    public CustomResult importTrfInfoData(String trfNo, Integer refSystemId, Object object, boolean needLabCode) {
        CustomResult result = new CustomResult();
        Map<String, Object> iLayerRspMaps = Maps.newHashMap();
        iLayerRspMaps.put(trfNo, object);
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(refSystemId);
        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = this.updateOrInsertTrfInfo(refSystem, null, iLayerRspMaps, needLabCode);
        if (!listCustomResult.isSuccess()) {
            return result.fail(listCustomResult.getMsg());
        }
        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            return result.fail("未获取到符合条件的trf信息，请检查数据！");
        }
        List<String> trfNos = trfs.stream().map(CustomerTrfInfoPO::getTrfNo).distinct().collect(Collectors.toList());
        logger.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystem.getRefSystemId(), trfNos);
        trfInfoExtMapper.batchInsert(trfs);
        result.setSuccess(true);
        return result;
    }


    public CustomResult importTrfInfoDataByInterface(String trfNo, Integer refSystemId, Object object, String labCode) {
        CustomResult result = new CustomResult();
        Map<String, Object> iLayerRspMaps = Maps.newHashMap();
        iLayerRspMaps.put(trfNo, object);
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(refSystemId);

        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            user = new UserInfo();
            user.setRegionAccount(USER_DEFAULT);
            user.setName(USER_DEFAULT);
            user.setCurrentLabCode(labCode);
            UserHelper.setLocalUser(user);
        }

        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = this.updateOrInsertTrfInfo(refSystem, null, iLayerRspMaps, true);
        if (!listCustomResult.isSuccess()) {
            return result.fail(listCustomResult.getMsg());
        }
        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            return result.fail("未获取到符合条件的trf信息，请检查数据！");
        }
        List<String> trfNos = trfs.stream().map(CustomerTrfInfoPO::getTrfNo).distinct().collect(Collectors.toList());
        logger.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystem.getRefSystemId(), trfNos);
        trfInfoExtMapper.batchInsert(trfs);
        result.setSuccess(true);
        return result;
    }

    /**
     * trf 导入接口
     *
     * @param reqObject
     * @return
     */
    public CustomResult importTrfNo(JsonNode reqObject) {
        CustomResult<Object> rspResult = new CustomResult<>();

        try {
            // 初始化请求上下文
            LabInfo labInfo = initializeRequestContext(rspResult);
            // 校验 JSON 请求
            TodoListBaseDataDTO data = checkJsonNode(reqObject, rspResult);
            // 获取客户配置
            CustomerConfigRsp conf = getCustomerConfig(data, rspResult);
            // 处理 TRF 导入
            handleTrfImport(data, conf, reqObject, rspResult, labInfo);
        } catch (Exception e) {
            logger.error("Error in importTrfNo: ", e);
            throw e;
        }

        return rspResult;
    }

    public CustomResult importTrfNoOld(JsonNode reqObject) {
        CustomResult<Object> rspResult = new CustomResult<>();

        String productLineCode = ProductLineContextHolder.getProductLineCode();
        ProductLineType productLineType = ProductLineContextHolder.getProductLineType();
        int productLineId = productLineType.getProductLineId();
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String locationCode = StringUtils.EMPTY;
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(user.getCurrentLabCode());

        if (labInfo == null || com.sgs.framework.tool.utils.StringUtil.isBlank(labInfo.getLocationCode())) {
            return rspResult.fail("labCode not found");
        }
        if (labInfo != null) {
            locationCode = labInfo.getLocationCode();
        }

        CustomResult<TodoListBaseDataDTO> jsonDataRsp = this.checkJsonNode(reqObject);
        if (!jsonDataRsp.isSuccess()) {
            return rspResult.fail(jsonDataRsp.getMsg());
        }
        TodoListBaseDataDTO data = jsonDataRsp.getData();

        String customerGroupCode = data.getCustomerGroupCode();
        String batchNo = data.getBatchNo();
        List<String> sceneTypes = data.getSceneTypes();
        if (CollectionUtils.isEmpty(sceneTypes)) {
            return rspResult.fail("Please check the sceneType！");
        }
        Map<String, List<String>> requestMaps = data.getAllRequestMaps();
        if (CollectionUtils.isEmpty(requestMaps)) {
            return rspResult.fail("Please check the parameters!");
        }

        Integer refSystemId = data.getRefSystemId();
        CustomerConfigRsp conf = customerConfigClient.getCustomerConfig(refSystemId, productLineCode);
        if (conf == null) {
            return rspResult.fail(String.format("Get Customer(%s) Config Fail!", customerGroupCode));
        }
        // TODO SCI-900
        if (Objects.equals(refSystemId, 1)) {
            refSystemId = 2;
        }
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(conf.getRefSystemId());


        // TODO SL  TIC
        boolean firstTime = true;
        if (refSystem.getRefSystemId() == RefSystemIdEnum.Shein.getRefSystemId() &&
                reqObject.get("secondTime") != null) {
            firstTime = false;
        }
        if (firstTime && RefSystemIdEnum.check(data.getRefSystemId(),
                RefSystemIdEnum.TIC, RefSystemIdEnum.Shein, RefSystemIdEnum.SGSMart, RefSystemIdEnum.Septwolves,
                RefSystemIdEnum.F21, RefSystemIdEnum.JO_ANN, RefSystemIdEnum.Target, RefSystemIdEnum.BigLots,
                RefSystemIdEnum.Walmart,RefSystemIdEnum.Walmart_Group, RefSystemIdEnum.Veyer, RefSystemIdEnum.DollarTree)) {
            if (refSystem.getRefSystemId() == RefSystemIdEnum.TIC.getRefSystemId() ||
                    refSystem.getRefSystemId() == RefSystemIdEnum.SGSMart.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.Septwolves.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.F21.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.JO_ANN.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.Target.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.BigLots.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.Walmart.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.Walmart_Group.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.Veyer.getRefSystemId()
                    || refSystem.getRefSystemId() == RefSystemIdEnum.DollarTree.getRefSystemId()
            ) {
                TrfImportReq trfImportReq = new TrfImportReq();
                trfImportReq.setTrfNo(data.getTrfNo());
                trfImportReq.setRefSystemId(data.getRefSystemId());
                trfImportReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                // TODO  RequestId 规则
                trfImportReq.setRequestId(UUID.randomUUID().toString());
                trfImportReq.setSystemId(ProductLineType.check(ProductLineContextHolder.getProductLineId(), ProductLineType.SL) ? SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());
                trfImportReq.setLabCode(labInfo.getLaboratoryCode());
                trfImportReq.setSource("todoList");
                TrfImportResult trfDTO = sciTrfBizService.importTrf(trfImportReq);
                if (trfDTO != null) {
                    rspResult.setSuccess(true);
                    rspResult.setData(trfDTO);
                }
            } else if (refSystem.getRefSystemId() == RefSystemIdEnum.Shein.getRefSystemId()) {
                List<TrfImportResult> importResults = new ArrayList<>();
                for (String fieldKey : requestMaps.keySet()) {
                    if (!StringUtils.equalsIgnoreCase(fieldKey, TRF_NO)) {
                        continue;
                    }
                    List<String> keyValues = requestMaps.get(fieldKey);
                    if (CollectionUtils.isEmpty(keyValues)) {
                        continue;
                    }
                    for (String valueItem : keyValues) {
                        TrfImportReq trfImportReq = new TrfImportReq();
                        trfImportReq.setTrfNo(valueItem);
                        trfImportReq.setRefSystemId(refSystem.getRefSystemId());
                        trfImportReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        trfImportReq.setRequestId(UUID.randomUUID().toString());
                        trfImportReq.setLabCode(labInfo.getLaboratoryCode());
                        trfImportReq.setLabId(Func.toInteger(labInfo.getLaboratoryID()));
                        trfImportReq.setSystemId(ProductLineType.check(ProductLineContextHolder.getProductLineId(), ProductLineType.SL) ? SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());
                        trfImportReq.setSource("todoList");
                        TrfImportResult trfDTO = sciTrfBizService.importTrf(trfImportReq);
                        if (trfDTO != null) {
                            importResults.add(trfDTO);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(importResults)) {
                    rspResult.setSuccess(true);
                    rspResult.setData(importResults);
                }
            }
            return rspResult;
        }


        String dffFormGroupId = conf.getDffFormGroupId();
        String gridFormGroupId = conf.getGridFormGroupId();

//        UserInfo user = UserHelper.getLocalUser();
//        if (user == null) {
//            return rspResult.fail("get user fail!");
//        }
//        String locationCode = StringUtils.EMPTY;
//        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(user.getCurrentLabCode());
//
//        if (labInfo == null || com.sgs.framework.tool.utils.StringUtil.isBlank(labInfo.getLocationCode())) {
//            return rspResult.fail("labCode not found");
//        }
//        if (labInfo != null) {
//            locationCode = labInfo.getLocationCode();
//        }

        Map<String, Object> iLayerRspMaps = Maps.newHashMap();
        List<String> errorMsg = Lists.newArrayList();
        for (String fieldKey : requestMaps.keySet()) {
            if (!StringUtils.equalsIgnoreCase(fieldKey, TRF_NO)) {
                continue;
            }
            List<String> keyValues = requestMaps.get(fieldKey);
            if (CollectionUtils.isEmpty(keyValues)) {
                continue;
            }
            for (String valueItem : keyValues) {
                ObjectMapper objectMapper = new ObjectMapper();
                ObjectNode objectNode = objectMapper.createObjectNode();

                objectNode.put(fieldKey, valueItem);
                objectNode.put(PRODUCT_LINE_CODE, productLineCode);
                objectNode.put(CUSTOMER_GROUP_CODE, customerGroupCode);
                objectNode.put(DFF_FORM_GROUP_ID, dffFormGroupId);
                objectNode.put(GRID_FORM_GROUP_ID, gridFormGroupId);
                objectNode.put(REF_SYSTEM_ID, data.getRefSystemId());
                objectNode.put(LOCATION_CODE, locationCode);
                String languageId = JsonNodeUtils.getValueFromJson(reqObject, LANGUAGE_ID);
                if (Func.isNotEmpty(languageId)) {
                    objectNode.put(LANGUAGE_ID, languageId);
                }
                // TODO SCI-900
//                if (RefSystemIdEnum.check(data.getRefSystemId(), RefSystemIdEnum.SGSMart)) {
                if (RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.SGSMart)) {
                    objectNode.put(ACTION, GET_SGS_MART_TRF_INFO);
                }
                Map<String, String> stringStringMap = objectMapper.convertValue(objectNode, new TypeReference<Map<String, String>>() {
                });
                // 根据上步中的结果（Map）请求 iLayer接口（/openapi/sync/getInfo） ???
                BaseResponse response = iLayerClient.syncGetInfo(stringStringMap);
                //返回的结果，查询逐条判断数据库中是否已存在。
                if (response.isSuccess() && !ObjectUtils.isEmpty(response.getData())) {
                    iLayerRspMaps.put(valueItem, response.getData());
                } else {
                    errorMsg.add(response.getMessage());
                }
            }
        }
        if (!errorMsg.isEmpty()) {
            return rspResult.fail(StringUtils.join(errorMsg, "\r\n"));
        }

        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = this.updateOrInsertTrfInfo(refSystem, batchNo, iLayerRspMaps, true);
        if (!listCustomResult.isSuccess()) {
            return rspResult.fail(listCustomResult.getMsg());
        }
        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            return rspResult.fail("未获取到符合条件的trf信息，请检查数据！");
        }

        // SGSMart 需要将模糊搜索表中的数据删除 trfs
        List<String> trfNos = trfs.stream().map(CustomerTrfInfoPO::getTrfNo).distinct().collect(Collectors.toList());
        logger.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystem.getRefSystemId(), trfNos);

        // tb_customer_trf_info保存完整信息，tb_trf保存关键信息
        rspResult.setSuccess(this.doubleWriteTrf(trfs, labInfo));

        rspResult.setData(trfNos);
        return rspResult;
    }

    public LabInfo initializeRequestContext(CustomResult<Object> rspResult) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        ProductLineType productLineType = ProductLineContextHolder.getProductLineType();
        int productLineId = productLineType.getProductLineId();
        // 获取登录用户信息
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            rspResult.fail("get user fail!");
            throw new RuntimeException("get user fail!");
        }
        // 获取实验室信息
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(user.getCurrentLabCode());
        if (labInfo == null || com.sgs.framework.tool.utils.StringUtil.isBlank(labInfo.getLocationCode())) {
            rspResult.fail("labCode not found");
            throw new RuntimeException("labCode not found");
        }
        return labInfo;
    }

    public TodoListBaseDataDTO checkJsonNode(JsonNode reqObject, CustomResult<Object> rspResult) {
        CustomResult<TodoListBaseDataDTO> jsonDataRsp = this.checkJsonNode(reqObject);
        if (!jsonDataRsp.isSuccess()) {
            rspResult.fail(jsonDataRsp.getMsg());
            throw new RuntimeException(jsonDataRsp.getMsg());
        }
        return jsonDataRsp.getData();
    }

    public CustomerConfigRsp getCustomerConfig(TodoListBaseDataDTO data, CustomResult<Object> rspResult) {
        Integer refSystemId = data.getRefSystemId();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        CustomerConfigRsp conf = customerConfigClient.getCustomerConfig(refSystemId, productLineCode);
        if (conf == null) {
            rspResult.fail(String.format("Get Customer(%s) Config Fail!", data.getCustomerGroupCode()));
            throw new RuntimeException(String.format("Get Customer(%s) Config Fail!", data.getCustomerGroupCode()));
        }
        return conf;
    }

    private Map<String, Object> fetchILayerResponses(CustomerConfigRsp conf, TodoListBaseDataDTO data, String dffFormGroupId, String gridFormGroupId, String locationCode, JsonNode reqObject, CustomResult<Object> rspResult) {
        Map<String, Object> iLayerRspMaps = Maps.newHashMap();
        List<String> errorMsg = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        for (String fieldKey : data.getAllRequestMaps().keySet()) {
            if (!StringUtils.equalsIgnoreCase(fieldKey, TRF_NO)) {
                continue;
            }
            List<String> keyValues = data.getAllRequestMaps().get(fieldKey);
            if (CollectionUtils.isEmpty(keyValues)) {
                continue;
            }

            for (String valueItem : keyValues) {
                ObjectNode objectNode = createObjectNode(fieldKey, valueItem, conf, data, dffFormGroupId, gridFormGroupId, locationCode, reqObject);
                Map<String, String> stringStringMap = objectMapper.convertValue(objectNode, new TypeReference<Map<String, String>>() {
                });
                BaseResponse response = iLayerClient.syncGetInfo(stringStringMap);
                if (response.isSuccess() && !ObjectUtils.isEmpty(response.getData())) {
                    iLayerRspMaps.put(valueItem, response.getData());
                } else {
                    errorMsg.add(response.getMessage());
                }
            }
        }

        if (!errorMsg.isEmpty()) {
            rspResult.fail(StringUtils.join(errorMsg, "\r\n"));
        }

        return iLayerRspMaps;
    }

    private ObjectNode createObjectNode(String fieldKey, String valueItem, CustomerConfigRsp conf, TodoListBaseDataDTO data, String dffFormGroupId, String gridFormGroupId, String locationCode, JsonNode reqObject) {
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode objectNode = objectMapper.createObjectNode();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        objectNode.put(fieldKey, valueItem);
        objectNode.put(PRODUCT_LINE_CODE, productLineCode);
        objectNode.put(CUSTOMER_GROUP_CODE, conf.getCustomerGroupCode());
        objectNode.put(DFF_FORM_GROUP_ID, dffFormGroupId);
        objectNode.put(GRID_FORM_GROUP_ID, gridFormGroupId);
        objectNode.put(REF_SYSTEM_ID, data.getRefSystemId());
        objectNode.put(LOCATION_CODE, locationCode);
        String languageId = JsonNodeUtils.getValueFromJson(reqObject, LANGUAGE_ID);
        if (Func.isNotEmpty(languageId)) {
            objectNode.put(LANGUAGE_ID, languageId);
        }
        // TODO SCI-900
//                if (RefSystemIdEnum.check(data.getRefSystemId(), RefSystemIdEnum.SGSMart)) {
        if (RefSystemIdEnum.check(data.getRefSystemId(), RefSystemIdEnum.SGSMart)) {
            objectNode.put(ACTION, GET_SGS_MART_TRF_INFO);
        }

        // 添加其他请求参数
        if (reqObject != null) {
            reqObject.fieldNames().forEachRemaining(fieldName -> {
                JsonNode fieldValue = reqObject.get(fieldName);
                if (fieldValue != null) {
                    objectNode.set(fieldName, fieldValue);
                }
            });
        }

        return objectNode;
    }

    private void handleTrfImport(TodoListBaseDataDTO data, CustomerConfigRsp conf, JsonNode reqObject, CustomResult<Object> rspResult, LabInfo labInfo) {
        RefSystemIdEnum refSystem = getRefSystemIdEnum(data, conf, reqObject, rspResult, labInfo);
        if (refSystem == null) return;

        updateOrInserTrfInfo(data, conf, reqObject, rspResult, labInfo, refSystem);
    }

    private void updateOrInserTrfInfo(TodoListBaseDataDTO data, CustomerConfigRsp conf, JsonNode reqObject, CustomResult<Object> rspResult, LabInfo labInfo, RefSystemIdEnum refSystem) {
        Map<String, Object> iLayerRspMaps = getiLayerRspMaps(data, conf, reqObject, rspResult, labInfo);
        if (iLayerRspMaps == null) return;

        updateOrInsertTrfInfo(data, rspResult, labInfo, refSystem, iLayerRspMaps);
    }

    public @Nullable Map<String, Object> getiLayerRspMaps(TodoListBaseDataDTO data, CustomerConfigRsp conf, JsonNode reqObject, CustomResult<Object> rspResult, LabInfo labInfo) {
        String dffFormGroupId = conf.getDffFormGroupId();
        String gridFormGroupId = conf.getGridFormGroupId();
        String locationCode = StringUtils.EMPTY;
        if (labInfo != null) {
            locationCode = labInfo.getLocationCode();
        }

        if (Objects.equals(data.getRefSystemId(), 1)) {
            data.setRefSystemId(2);
        }

        Map<String, Object> iLayerRspMaps = fetchILayerResponses(conf, data, dffFormGroupId, gridFormGroupId, locationCode, reqObject, rspResult);
        if (Func.isEmpty(iLayerRspMaps)) {
            return null;
        }
        return iLayerRspMaps;
    }

    private @Nullable RefSystemIdEnum getRefSystemIdEnum(TodoListBaseDataDTO data, CustomerConfigRsp conf, JsonNode reqObject, CustomResult<Object> rspResult, LabInfo labInfo) {
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(conf.getRefSystemId());
        boolean firstTime = isFirstTimeImport(refSystem, reqObject);

        if (firstTime && RefSystemIdEnum.check(data.getRefSystemId(), refSystemIdList.toArray(new RefSystemIdEnum[0]))) {
            List<TrfImportResult> importResults = getTrfImportResults(data, refSystem, reqObject, labInfo);
            if (!CollectionUtils.isEmpty(importResults)) {
                rspResult.setSuccess(true);
                rspResult.setData(importResults);
            }
            return null;
        }
        return refSystem;
    }

    public void updateOrInsertTrfInfo(TodoListBaseDataDTO data, CustomResult<Object> rspResult, LabInfo labInfo, RefSystemIdEnum refSystem, Map<String, Object> iLayerRspMaps) {
        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = updateOrInsertTrfInfo(refSystem, data.getBatchNo(), iLayerRspMaps, true);
        if (!listCustomResult.isSuccess()) {
            rspResult.fail(listCustomResult.getMsg());
            return;
        }
        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            rspResult.fail("未获取到符合条件的trf信息，请检查数据！");
            return;
        }

        List<String> trfNos = deleteTrfTodoInfo(trfs, refSystem);
        rspResult.setSuccess(doubleWriteTrf(trfs, labInfo));
        rspResult.setData(trfNos);
    }

    private List<String> deleteTrfTodoInfo(List<CustomerTrfInfoPO> trfs, RefSystemIdEnum refSystem) {
        // SGSMart 需要将模糊搜索表中的数据删除 trfs
        List<String> trfNos = trfs.stream().map(CustomerTrfInfoPO::getTrfNo).distinct().collect(Collectors.toList());
        logger.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystem.getRefSystemId(), trfNos);
        return trfNos;
    }

    private boolean isFirstTimeImport(RefSystemIdEnum refSystem, JsonNode reqObject) {
        if (refSystem.getRefSystemId() == RefSystemIdEnum.Shein.getRefSystemId() &&
                reqObject.get("secondTime") != null) {
            return false;
        }
        return true;
    }

    private List<TrfImportResult> getTrfImportResults(TodoListBaseDataDTO data, RefSystemIdEnum refSystem, JsonNode reqObject, LabInfo labInfo) {
        List<TrfImportResult> importResults = new ArrayList<>();
        if (refSystem.getRefSystemId() == RefSystemIdEnum.Shein.getRefSystemId()) {
            importResults = getTrfImportResults(data.getAllRequestMaps(), importResults, refSystem, data, labInfo);
        } else {
            importResults = getTrfImportResult(data.getTrfNo(), data.getRefSystemId(), data, labInfo);
        }
        return importResults;
    }


    private List<TrfImportResult> getTrfImportResults(Map<String, List<String>> requestMaps, List<TrfImportResult> importResults, RefSystemIdEnum refSystem, TodoListBaseDataDTO data, LabInfo labInfo) {
        for (String fieldKey : requestMaps.keySet()) {
            if (!StringUtils.equalsIgnoreCase(fieldKey, TRF_NO)) {
                continue;
            }
            List<String> keyValues = requestMaps.get(fieldKey);
            if (CollectionUtils.isEmpty(keyValues)) {
                continue;
            }
            for (String valueItem : keyValues) {
                importResults = getTrfImportResult(valueItem, refSystem.getRefSystemId(), data, labInfo);
            }
        }
        return importResults;
    }

    public List<TrfImportResult> getTrfImportResult(String trfNo, Integer refSystemId, TodoListBaseDataDTO data, LabInfo labInfo) {
        List<TrfImportResult> importResults = new ArrayList<>();
        TrfImportReq trfImportReq = buildTrfImportReq(trfNo, refSystemId, labInfo);
        TrfImportResult trfDTO = sciTrfBizService.importTrf(trfImportReq);
        if (trfDTO != null) {
            importResults.add(trfDTO);
        }
        return importResults;
    }

    protected static @NotNull TrfImportReq buildTrfImportReq(String trfNo, Integer refSystemId, LabInfo labInfo) {
        TrfImportReq trfImportReq = new TrfImportReq();
        trfImportReq.setTrfNo(trfNo);
        trfImportReq.setRefSystemId(refSystemId);
        trfImportReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        // TODO  RequestId 规则
        trfImportReq.setRequestId(UUID.randomUUID().toString());
        trfImportReq.setSystemId(ProductLineType.check(ProductLineContextHolder.getProductLineId(), ProductLineType.SL) ? SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());
        trfImportReq.setLabCode(labInfo.getLaboratoryCode());
        trfImportReq.setLabId(Func.toInteger(labInfo.getLaboratoryID()));
        trfImportReq.setSource("todoList");
        return trfImportReq;
    }

    private boolean doubleWriteTrf(List<CustomerTrfInfoPO> trfs, LabInfo labInfo) {
        return transactionTemplate.execute(trans -> {
            boolean saveSuccess = this.saveCustomerTrf(trfs) && this.saveTbTrfKeyFields(trfs, labInfo);
            if (!saveSuccess) {
                trans.setRollbackOnly();
            }
            return saveSuccess;
        });
    }

    private boolean saveCustomerTrf(List<CustomerTrfInfoPO> trfs) {
        return trfInfoExtMapper.batchInsert(trfs) > 0;
    }

    private boolean saveTbTrfKeyFields(List<CustomerTrfInfoPO> trfs, LabInfo labInfo) {
        if (CollectionUtils.isEmpty(trfs)) {
            return false;
        }

        // 过滤数据库已经存在且有效的trf
        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria()
                .andRefSystemIdEqualTo(CollUtil.getFirst(trfs).getRefSystemId())
                .andTrfNoIn(trfs.stream().map(CustomerTrfInfoPO::getTrfNo).collect(Collectors.toList()))
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> dbTrfInfoList = trfInfoMapper.selectByExample(trfInfoExample);
        Map<String, TrfInfoPO> dbTrfInfoMap = dbTrfInfoList.stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(TrfInfoPO::getTrfNo, Function.identity(), (a, b) -> a));

        List<TrfInfoPO> trfInfoList = trfs.stream().map(customerTrf -> {
            //trfStatus，trfId，状态标记，refSystemId，labCode
            TrfInfoPO trfInfoPO = new TrfInfoPO();
            // 数据库存在 active的 trf取旧的trfId
            TrfInfoPO dbActiveTrf = dbTrfInfoMap.get(customerTrf.getTrfNo());
            if (dbActiveTrf != null) {
                trfInfoPO.setId(dbActiveTrf.getId());
            } else {
                long trfId = (customerTrf.getId() == null) ? IdUtil.snowflakeId() : customerTrf.getId().longValue();
                trfInfoPO.setId(trfId);
            }
            trfInfoPO.setTrfNo(customerTrf.getTrfNo());
            trfInfoPO.setStatus(customerTrf.getTrfStatus());
            trfInfoPO.setPendingFlag(PendingFlagEnum.UnPending.getType());
            trfInfoPO.setRefSystemId(customerTrf.getRefSystemId());
            trfInfoPO.setLabCode(customerTrf.getLabCode());
            trfInfoPO.setLabId(Func.toInteger(labInfo.getLaboratoryID(), null));
            trfInfoPO.setBuId(Func.toInteger(labInfo.getProductLineID(), null));
            trfInfoPO.setBuCode(customerTrf.getProductLineCode());
            trfInfoPO.setCreatedBy(customerTrf.getCreatedBy());
            trfInfoPO.setCreatedDate(customerTrf.getCreatedDate());
            trfInfoPO.setSource(TrfSourceType.TRF2Order.getSourceType());
            trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
            trfInfoPO.setModifiedBy(customerTrf.getModifiedBy());
            trfInfoPO.setModifiedDate(customerTrf.getModifiedDate());
            trfInfoPO.setIntegrationLevel("2");
            //FIXME TRF Todo List组件未来需要上送systemId信息
            trfInfoPO.setSystemId(Objects.equals(customerTrf.getProductLineCode(), ProductLineType.SL.getProductLineAbbr()) ?
                    SgsSystem.SODA.getSgsSystemId() : SgsSystem.GPO.getSgsSystemId());
            return trfInfoPO;
        }).collect(Collectors.toList());
        return trfInfoMapper.batchInsert(trfInfoList) > 0;
    }

    /**
     * @param reqObject
     * @return
     */
    public CustomResult updateTrfInfo(UpdateTrfInfoReq reqObject) {

        CustomResult<Object> rspResult = new CustomResult<>();
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        Integer refSystemId = reqObject.getRefSystemId();
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(refSystemId);
        if (refSystem == null) {
            return rspResult.fail("PLEASE CHECK refSystemId！");
        }
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String customerGroupCode = reqObject.getCustomerGroupCode();

        if (CollectionUtils.isEmpty(reqObject.getTrfNos()) || StringUtils.isBlank(customerGroupCode)) {
            return rspResult.fail("Please Check TrfNos/customerGroupCode");
        }

        CustomerConfigRsp customerConfig = customerConfigClient.getCustomerConfig(refSystemId, productLineCode);
        if (customerConfig == null) {
            return rspResult.fail(String.format("Get Customer(%s) Config Fail!", customerGroupCode));
        }
        String dffFormGroupId = customerConfig.getDffFormGroupId();
        String gridFormGroupId = customerConfig.getGridFormGroupId();

        Map<String, Object> iLayerRspMaps = Maps.newHashMap();
        List<String> errorMsg = Lists.newArrayList();
        for (String trfNo : reqObject.getTrfNos()) {
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode objectNode = objectMapper.createObjectNode();

            objectNode.put(TRF_NO, trfNo);
            objectNode.put(PRODUCT_LINE_CODE, productLineCode);
            objectNode.put(CUSTOMER_GROUP_CODE, customerGroupCode);
            objectNode.put(DFF_FORM_GROUP_ID, dffFormGroupId);
            objectNode.put(GRID_FORM_GROUP_ID, gridFormGroupId);
            // TODO SCI-900
            objectNode.put(REF_SYSTEM_ID, reqObject.getRefSystemId());
            if (Objects.equals(reqObject.getRefSystemId(), 2)) {
                objectNode.put(REF_SYSTEM_ID, 1);
            }

            Map<String, String> stringStringMap = objectMapper.convertValue(objectNode, new TypeReference<Map<String, String>>() {
            });
            // 根据上步中的结果（Map）请求 iLayer接口（/openapi/sync/getInfo） ???
            BaseResponse response = iLayerClient.syncGetInfo(stringStringMap);
            //返回的结果，查询逐条判断数据库中是否已存在。
            if (response.isSuccess() && !ObjectUtils.isEmpty(response.getData())) {
                iLayerRspMaps.put(trfNo, response.getData());
            } else {
                errorMsg.add(response.getMessage());
            }
        }
        if (!errorMsg.isEmpty()) {
            return rspResult.fail(StringUtils.join(errorMsg, "\r\n"));
        }
        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = this.updateOrInsertTrfInfo(refSystem, StringUtils.EMPTY, iLayerRspMaps, true);
        if (!listCustomResult.isSuccess()) {
            return rspResult.fail(listCustomResult.getMsg());
        }
        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            return rspResult.fail("未获取到符合条件的trf信息，请检查数据！");
        }
        rspResult.setSuccess(trfInfoExtMapper.batchInsert(trfs) > 0);
        return rspResult;
    }

    /**
     * @param refSystem
     * @param batchNo
     * @param iLayerRspMaps
     * @return
     */
    private CustomResult<List<CustomerTrfInfoPO>> updateOrInsertTrfInfo(RefSystemIdEnum refSystem, String batchNo, Map<String, Object> iLayerRspMaps, boolean needLabCode) {
        CustomResult<List<CustomerTrfInfoPO>> rspResult = new CustomResult<>(true);

        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String regionAccount = user.getRegionAccount();
        String currentLabCode = user.getCurrentLabCode();
        if (needLabCode && Func.isEmpty(currentLabCode)) {
            return rspResult.fail("labCode not found");
        }

        List<CustomerTrfInfoPO> insertOrUpdate = Lists.newArrayList();

        List<String> errorMsg = Lists.newArrayList();
        for (String objectKey : iLayerRspMaps.keySet()) {
            Object object = iLayerRspMaps.get(objectKey);

            String toJSONString = JSONObject.toJSONString(object);
            JSONObject jsonObject = JSONObject.parseObject(toJSONString);

            JSONObject detail = (JSONObject) jsonObject.get(DETAIL);
            if (detail == null) {
                continue;
            }
            // 按照客户解析 trfNo
            String trfNo = this.parseTrfNo(detail, refSystem);
            if (StringUtils.isBlank(trfNo) || StringUtils.equalsIgnoreCase(trfNo, "null")) {
                errorMsg.add(String.format("TrfNo(%s)未找到相应的客户TrfNo，请检查数据!", objectKey));
                continue;
            }

            // POSL-5211 TIC 暂时只支持 一个 groupNo
            if (refSystem == RefSystemIdEnum.TIC && detail.containsKey(SAMPLEFORM) && detail.get(SAMPLEFORM) != null) {
                Object sampleFormStr = jsonObject.get(SAMPLEFORM);
                List<SampleFormDTO> sampleFormDTOS = JSONObject.parseArray(JSONObject.toJSONString(sampleFormStr), SampleFormDTO.class);
                if (!CollectionUtils.isEmpty(sampleFormDTOS)) {
                    Set<String> groupNos = sampleFormDTOS.stream().map(SampleFormDTO::getGroupNo).collect(Collectors.toSet());
                    if (groupNos.size() != 1) {
                        errorMsg.add(String.format("TRF(%s) contains multiple samples and cannot be imported", objectKey));
                        continue;
                    }
                }
            }

            CustomerTrfInfoRsp oldTrf = trfInfoExtMapper.getTrfInfo(refSystem.getRefSystemId(), trfNo);
            // 0: Inactive, 1: Active
            if (oldTrf != null && TrfStatusEnum.check(oldTrf.getTrfStatus(), TrfStatusEnum.Canceled)) {
                errorMsg.add(String.format("当前TrfNo(%s)已被取消！", objectKey));
                continue;
            }
            //TODO 校验trfno 是否已经绑定enquiryNo


            // 获取模板
            String dffFormId = (String) jsonObject.get(DFF_FORM_ID);
            String gridFormId = (String) jsonObject.get(GRID_FORM_ID);

            CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
            if (oldTrf != null) {
                trf.setId(oldTrf.getId());
            } else {
                trf.setId(idService.nextId());
            }
            trf.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            trf.setRefSystemId(refSystem.getRefSystemId());//客户系统Id,
            trf.setTrfNo(trfNo);
            trf.setBatchNo(batchNo); //批次号
            trf.setObjectNo(objectKey);
            if (StringUtils.isBlank(currentLabCode)) {
                Optional<String> labCodeFromRequest = Optional.ofNullable(detail.get("labCode")).map(Object::toString);
                if (labCodeFromRequest.isPresent()) {
                    currentLabCode = labCodeFromRequest.get();
                } else {
                    currentLabCode = Func.toStr(JSONPath.eval(detail, LAB_CODE));
                }
            }
            trf.setLabCode(currentLabCode);
            trf.setDffFormId(dffFormId);
            trf.setGridFormId(gridFormId);
            trf.setContent(detail != null ? detail.toJSONString() : null);
            trf.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
            if (refSystem == RefSystemIdEnum.SGSMart || refSystem == RefSystemIdEnum.F21
                    || refSystem == RefSystemIdEnum.JO_ANN || refSystem == RefSystemIdEnum.Target
                    || refSystem == RefSystemIdEnum.BigLots || refSystem == RefSystemIdEnum.Walmart || refSystem ==RefSystemIdEnum.Walmart_Group
                    || refSystem == RefSystemIdEnum.Veyer || refSystem == RefSystemIdEnum.Amazon || refSystem == RefSystemIdEnum.DollarTree) {
                if (detail.containsKey(HEADERS)) {
                    Object headers = detail.get(HEADERS);
                    if (headers != null) {
                        String headersJSONString = JSONObject.toJSONString(headers);
                        JSONObject headersObject = JSONObject.parseObject(headersJSONString);
                        trf.setTrfDate(DateUtils.parseDate(String.valueOf(headersObject.get(TRF_SUBMISSION_DATE))));
                        String buCode = String.valueOf(headersObject.get(BU_CODE));
//                        String locationCode = String.valueOf(headersObject.get(LOCATION_CODE));
                        String locationCode = Func.toStr(JSONPath.eval(detail, LAB_CODE));
                        if (!StringUtils.equalsIgnoreCase(currentLabCode, locationCode)) {
                            errorMsg.add(String.format("SGSMart-TrfNo(%s)LabCode不匹配，请检查数据!", trfNo));
                            continue;
                        }
                    }
                }
            }
            // 0: inactive, 1: active
            trf.setActiveIndicator(1);
            trf.setCreatedBy(regionAccount);
            trf.setCreatedDate(DateUtils.getNow());
            trf.setModifiedBy(regionAccount);
            trf.setModifiedDate(DateUtils.getNow());

            // 由于业务需要，需要将 希音返回的数据 做处理后再存库
            this.appendContentField(trf);
            // 不存在，新增
            if (oldTrf == null) {
                insertOrUpdate.add(trf);
                continue;
            }
            // 已存在，更新，如果已开单，则不更新;
            // 查询是否已开单
            List<TrfOrderPO> boundTrfInfo = boundTrfRelExtMapper.getBoundTrfInfoList(Lists.newArrayList(trfNo), BoundStatus.BoundHasOrder.getType());
            boolean checkCustomer = trfDomainService.checkCustomer(refSystem.getRefSystemId());
            if (!CollectionUtils.isEmpty(boundTrfInfo) && !checkCustomer) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DUPLICATEDATA);
                throw new CustomerBizException(errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), String.format("trf(%s)已经存在，不再进行导入操作。", objectKey));
            }
            insertOrUpdate.add(trf);
        }
        rspResult.setData(insertOrUpdate);

        if (!CollectionUtils.isEmpty(errorMsg)) {
            rspResult.setMsg(StringUtils.join(errorMsg, "\r\n"));
            rspResult.setSuccess(false);
        }
        return rspResult;
    }


    private CustomResult<List<CustomerTrfInfoPO>> updateOrInsertTrfInfoForInterface(RefSystemIdEnum refSystem, String currentLabCode, Map<String, Object> iLayerRspMaps) {
        CustomResult<List<CustomerTrfInfoPO>> rspResult = new CustomResult<>(true);

        List<CustomerTrfInfoPO> insertOrUpdate = Lists.newArrayList();

        List<String> errorMsg = Lists.newArrayList();
        for (String objectKey : iLayerRspMaps.keySet()) {
            Object object = iLayerRspMaps.get(objectKey);

            String toJSONString = JSONObject.toJSONString(object);
            JSONObject jsonObject = JSONObject.parseObject(toJSONString);

            JSONObject detail = (JSONObject) jsonObject.get(DETAIL);
            if (detail == null) {
                continue;
            }
            // 按照客户解析 trfNo
            String trfNo = this.parseTrfNo(detail, refSystem);

            if (StringUtils.isBlank(trfNo) || StringUtils.equalsIgnoreCase(trfNo, "null")) {
                errorMsg.add(String.format("TrfNo(%s)未找到相应的客户TrfNo，请检查数据!", objectKey));
                continue;
            }
            CustomerTrfInfoRsp oldTrf = trfInfoExtMapper.getTrfInfo(refSystem.getRefSystemId(), trfNo);
            // 0: Inactive, 1: Active
            if (oldTrf != null && TrfStatusEnum.check(oldTrf.getTrfStatus(), TrfStatusEnum.Canceled)) {
                errorMsg.add(String.format("当前TrfNo(%s)已被取消！", objectKey));
                continue;
            }
            //TODO 校验trfno 是否已经绑定enquiryNo


            // 获取模板
            String dffFormId = (String) jsonObject.get(DFF_FORM_ID);
            String gridFormId = (String) jsonObject.get(GRID_FORM_ID);

            CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
            if (oldTrf != null) {
                trf.setId(oldTrf.getId());
            }
            trf.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            trf.setRefSystemId(refSystem.getRefSystemId());//客户系统Id,
            trf.setTrfNo(trfNo);
            trf.setObjectNo(objectKey);
            trf.setLabCode(currentLabCode);
            trf.setDffFormId(dffFormId);
            trf.setGridFormId(gridFormId);
            trf.setContent(detail != null ? detail.toJSONString() : null);
            trf.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
            if (refSystem == RefSystemIdEnum.SGSMart || refSystem == RefSystemIdEnum.F21
                    || refSystem == RefSystemIdEnum.JO_ANN || refSystem == RefSystemIdEnum.Target
                    || refSystem == RefSystemIdEnum.BigLots || refSystem == RefSystemIdEnum.Walmart || refSystem == RefSystemIdEnum.Walmart_Group
                    || refSystem == RefSystemIdEnum.Veyer || refSystem == RefSystemIdEnum.Amazon || refSystem == RefSystemIdEnum.DollarTree) {
                if (detail.containsKey(HEADERS)) {
                    Object headers = detail.get(HEADERS);
                    if (headers != null) {
                        String headersJSONString = JSONObject.toJSONString(headers);
                        JSONObject headersObject = JSONObject.parseObject(headersJSONString);
                        trf.setTrfDate(DateUtils.parseDate(String.valueOf(headersObject.get(TRF_SUBMISSION_DATE))));
                        String buCode = String.valueOf(headersObject.get(BU_CODE));
                        String locationCode = String.valueOf(headersObject.get(LOCATION_CODE));
                        if (!StringUtils.equalsIgnoreCase(currentLabCode, String.format("%s %s", locationCode, buCode))) {
                            errorMsg.add(String.format("SGSMart-TrfNo(%s)LabCode不匹配，请检查数据!", trfNo));
                            continue;
                        }
                    }
                }
            }
            // 0: inactive, 1: active
            trf.setActiveIndicator(1);

            trf.setCreatedDate(DateUtils.getNow());

            trf.setModifiedDate(DateUtils.getNow());

            // 由于业务需要，需要将 希音返回的数据 做处理后再存库
            this.appendContentField(trf);
            // 不存在，新增
            if (oldTrf == null) {
                insertOrUpdate.add(trf);
                continue;
            }
            // 已存在，更新，如果已开单，则不更新;
            // 查询是否已开单
            List<TrfOrderPO> boundTrfInfo = boundTrfRelExtMapper.getBoundTrfInfoList(Lists.newArrayList(trfNo), BoundStatus.BoundHasOrder.getType());
            if (!CollectionUtils.isEmpty(boundTrfInfo)) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DUPLICATEDATA);
                throw new CustomerBizException(errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), String.format("trf(%s)已经存在，不再进行导入操作。", objectKey));
            }
            insertOrUpdate.add(trf);
        }
        rspResult.setData(insertOrUpdate);

        if (!CollectionUtils.isEmpty(errorMsg)) {
            rspResult.setMsg(StringUtils.join(errorMsg, "\r\n"));
            rspResult.setSuccess(false);
        }
        return rspResult;
    }

    /**
     * 页面搜索展示列表接口
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTrfInfoList(JsonNode reqObject) {
        CustomResult rspResult = new CustomResult();
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String currentLabCode = user.getCurrentLabCode();

        // 解析固定参数
        CustomResult<TodoListBaseDataDTO> jsonDataRsp = this.checkJsonNode(reqObject);
        if (!jsonDataRsp.isSuccess()) {
            return rspResult.fail(jsonDataRsp.getMsg());
        }
        TodoListBaseDataDTO data = jsonDataRsp.getData();
        Map<String, List<String>> requestMaps = data.getAllRequestMaps();
        int productLineId = data.getProductLineId();
        String customerGroupCode = data.getCustomerGroupCode();
        String packageBarcode = data.getPackageBarcode();
        String createDate = data.getCreateDate();
        String endDate = data.getEndDate();
        String trfStartDate = data.getTrfStartDate();
        String trfEndDate = data.getTrfEndDate();
        String trfNo = data.getTrfNo();
        String orderNo = data.getOrderNo();
        String status = data.getStatus();
        List<String> createBys = data.getCreateBys();
        int page = data.getPage();
        int rows = data.getRows();

        SearchType searchType = data.getSearchType();

        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(data.getRefSystemId());
        if (refSystem == null) {
            return rspResult.fail("Please Check RefSystemId");
        }
        int refSystemId = refSystem.getRefSystemId();

        // 不用限制创建时间 已有分页
//        if (StringUtils.isBlank(createDate) || StringUtils.isBlank(endDate)) {
//            return rspResult.fail("Get createdDate/endDate Fail!");
//        }
        if (page == 0 || rows == 0) {
            return rspResult.fail("Get page/rows Fail!");
        }

        // 拼装参数时 需要排除掉 创建时间 创建人
        requestMaps.remove(CREATED_DATE);
        requestMaps.remove(CREATED_BY);
        requestMaps.remove(TRF_SUBMISSION_DATE);

        // 搜索时不需要在Content中搜索TRF_NO
        requestMaps.remove(TRF_NO);
        requestMaps.remove(PACKAGE_BARCODE);
        requestMaps.remove(ORDER_NO);
        requestMaps.remove(TRF_STATUS);

        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        if (StringUtils.isNotBlank(trfNo)) {
            searchTrfInfoDTO.setTrfNos(Lists.newArrayList(trfNo));
        }
        if (StringUtils.isNotBlank(orderNo)) {
            searchTrfInfoDTO.setOrderNo(orderNo);
        }
        searchTrfInfoDTO.setLabCode(currentLabCode);
        searchTrfInfoDTO.setCreateDate(createDate);
        searchTrfInfoDTO.setEndDate(endDate);
        searchTrfInfoDTO.setTrfStartDate(trfStartDate);
        searchTrfInfoDTO.setTrfEndDate(trfEndDate);
        searchTrfInfoDTO.setCreateBys(createBys);
        searchTrfInfoDTO.setRequestMaps(requestMaps);
        searchTrfInfoDTO.setRefSystemId(refSystemId);
        searchTrfInfoDTO.setStatus(status);
        if (StringUtils.isNotBlank(packageBarcode)) {
            searchTrfInfoDTO.setPackageBarcodes(Lists.newArrayList(packageBarcode));
        }
        if (ProductLineType.check(productLineId, ProductLineType.HL) &&
                RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier)
        ) {
            searchTrfInfoDTO.setHasConnect(true);
        }
        List<BoundTrfRelDTO> boundTrfRelDTOS = Lists.newArrayList();
        BoundTrfInfoSearchReq boundTrfInfoSearchReq = new BoundTrfInfoSearchReq();
        BeanUtils.copyProperties(searchTrfInfoDTO, boundTrfInfoSearchReq);

        Boolean createMoreOrder = JsonNodeUtils.getBooleanValueFromJson(reqObject, CREATE_MORE_ORDER);
        String mode = JsonNodeUtils.getValueFromJson(reqObject, MODE);
        boundTrfInfoSearchReq.setCreateMoreOrder(createMoreOrder);

        boundTrfInfoSearchReq.setSearchType(searchType);
        boundTrfInfoSearchReq.setPage(page);
        boundTrfInfoSearchReq.setRows(rows);
        // TODO SCI-900
        if (Objects.equals(boundTrfInfoSearchReq.getRefSystemId(), 1)) {
            boundTrfInfoSearchReq.setRefSystemId(2);
        }
        if (Objects.equals(mode, REMOTE)) {
            return getTrfInfoListByRemoteMode(refSystem, data, reqObject, boundTrfInfoSearchReq, rspResult);
        }
        boundTrfRelDTOS = trfDomainService.getBoundTrfInfoList(boundTrfInfoSearchReq);
        if (boundTrfRelDTOS.isEmpty()) {
            return rspResult.fail("没有符合条件的数据！");
        }
        // 结果处理
        List<Object> trfDatas = this.dealResultForSearch(boundTrfRelDTOS, refSystem, trfNo);

        ConvertDataReq reqData = new ConvertDataReq();
        // TODO SCI-900
        reqData.setRefSystemId(refSystemId);
        reqData.setCustomerGroupCode(customerGroupCode);

        maybeRewriteRefSystemId(refSystemId, reqData);
        reqData.setProductLineId(RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO) ? 0 : productLineId);
        reqData.setSceneTypes(Lists.newArrayList(searchType.getDesc()));

        // 请求 iLayer 获取 新结构
        JsonNode jsonNode = iLayerClient.convertData(reqData, trfDatas);

        if (Func.isEmpty(jsonNode)) {
            return rspResult.fail("获取TRF信息失败，请检查数据！");
        }

        JsonNode jsonNodeRsp = jsonNode.get(searchType.getDesc());
        if (jsonNodeRsp == null || !jsonNodeRsp.isArray()) {
            return rspResult.fail("请检查数据mapping配置（TO 开发）");
        }

        if (jsonNodeRsp.isArray()) {
            Iterator<JsonNode> it = jsonNodeRsp.iterator();
            List<JsonNode> objectNodes = Lists.newArrayList();
            while (it.hasNext()) {
                JsonNode childNode = it.next();
                JsonNode orderNoNode = childNode.get("orderNo");
                if (Func.isEmpty(orderNoNode) && searchType.check(SearchType.BoundListType)) {
                    ((ObjectNode) childNode).remove("createdBy");
                    ((ObjectNode) childNode).remove("createdDate");
                }
                objectNodes.add(childNode);
            }
            Collections.sort(objectNodes, (o1, o2) -> {
                // 假设createdDate是一个long类型字段
                if (o1.has("createdDate") && o2.has("createdDate")) {
                    String createdDate1 = o1.get("createdDate").asText();
                    String createdDate2 = o2.get("createdDate").asText();
                    if (StringUtil.isNotEmpty(createdDate1) && StringUtil.isNotEmpty(createdDate2)) {
                        return DateUtils.parseDate(createdDate2).compareTo(DateUtils.parseDate(createdDate1)); // 倒序排序
                    } else {
                        return ZERO;
                    }
                } else {
                    return ZERO;
                }
            });

            PageInfo<BoundTrfRelDTO> pageTestLine = PageInfo.of(boundTrfRelDTOS);
            PageInfo pageInfo = new PageInfo<>(objectNodes);
            pageInfo.setTotal(pageTestLine.getTotal());
            pageInfo.setPageNum(pageTestLine.getPageNum());
            pageInfo.setPages(pageTestLine.getPages());
            pageInfo.setSize(pageTestLine.getSize());
            pageInfo.setList(objectNodes);

            rspResult.setData(pageInfo);
            rspResult.setSuccess(true);
            return rspResult;
        }
        return rspResult;
    }

    @Nullable
    private CustomResult getTrfInfoListByRemoteMode(RefSystemIdEnum refSystem, TodoListBaseDataDTO data, JsonNode reqObject, BoundTrfInfoSearchReq boundTrfInfoSearchReq, CustomResult rspResult) {
        // TODO call 老唐，等待提供Remote信息
        GetCustomerConfigReq customerConfigReq = new GetCustomerConfigReq();
        customerConfigReq.setRefSystemId(boundTrfInfoSearchReq.getRefSystemId());
        customerConfigReq.setBuCode(boundTrfInfoSearchReq.getProductLineCode());
        customerConfigReq.setLabCode(boundTrfInfoSearchReq.getLabCode());
        customerConfigReq.setProductLineCode(boundTrfInfoSearchReq.getProductLineCode());
        ConfigInfo customerConfig = configClient.getCustomerConfig(customerConfigReq);
        if(Func.isEmpty(customerConfig) || Func.isBlank(customerConfig.getConfigValue())) {
            throw new BizException("Not Found Query Customer Trf Request Template!");
        }
        String statusStr = JsonNodeUtils.getValueFromJson(reqObject, "Status");
        boundTrfInfoSearchReq.setStatus(statusStr);
        String configValue = customerConfig.getConfigValue();
        CustomerGeneralConfig customerGeneralConfig = JSON.parseObject(configValue, CustomerGeneralConfig.class);
        boundTrfInfoSearchReq.setMockData(customerGeneralConfig.getMockData());
        String customerFilterFieldMapping = customerGeneralConfig.getCustomerFilterFieldMapping();
        if (Func.isEmpty(customerFilterFieldMapping)) {
            throw new BizException("Not Found customerFilterFieldMapping!");
        }
        JSON convert = jsonDataConvertor.convert(JSONObject.toJSONString(boundTrfInfoSearchReq), customerFilterFieldMapping);
        CustomResult o = iLayerClient.queryCustomerTrfList(convert);
        if (!o.isSuccess() || Func.isEmpty(o.getData())) {
            return rspResult.fail("没有符合条件的数据！");
        }
        Object total = JSONPath.eval(o.getData(), "$.total");
        Object limit = JSONPath.eval(o.getData(), "$.limit");
        Object offset = JSONPath.eval(o.getData(), "$.offset");
        Object dataList = JSONPath.eval(o.getData(), "$.list");
        if (Func.isEmpty(dataList) || !(dataList instanceof JSONArray)) {
            return rspResult.fail("没有符合条件的数据！");
        }
        fillBoundList(refSystem, data, boundTrfInfoSearchReq, rspResult, (JSONArray) dataList);
        PageInfo pageInfo = new PageInfo<>();
        pageInfo.setTotal(Long.parseLong(total.toString()));
        pageInfo.setPageNum(Integer.parseInt(offset.toString()));
        pageInfo.setSize(Integer.parseInt(limit.toString()));
        int totalPage = (int) Math.ceil((double) pageInfo.getTotal() / pageInfo.getSize());
        pageInfo.setPages(totalPage);
        pageInfo.setList((List) dataList);
        rspResult.setData(pageInfo);
        rspResult.setSuccess(true);
        return rspResult;
    }

    private void fillBoundList(RefSystemIdEnum refSystem, TodoListBaseDataDTO data, BoundTrfInfoSearchReq boundTrfInfoSearchReq, CustomResult rspResult, JSONArray dataList) {
        JSONArray dataJSONArray = dataList;
        List<String> trfNos = FastJSONUtils.toJSONObjectStream(dataJSONArray).map(item -> item.getString("RequestNo")).collect(Collectors.toList());
        List<TrfInfoPO> trfInfoList = trfDomainService.selectActiveAndNotCancelByTrfNos(trfNos);
        Map<String, String> sgsTrfNoMap = Optional.ofNullable(trfInfoList)
                .map(list -> list.stream()
                        .filter(trf -> Objects.nonNull(trf.getTrfNo()) && Objects.nonNull(trf.getSgsTrfNo()))
                        .collect(Collectors.toMap(TrfInfoPO::getTrfNo, TrfInfoPO::getSgsTrfNo, (v1, v2) -> v1))
                ).orElse(Collections.emptyMap());
        FastJSONUtils.toJSONObjectStream(dataJSONArray)
                .forEach(item ->
                        item.put("sgsTrfNo",
                                Optional.ofNullable(item.getString("RequestNo"))
                                        .filter(sgsTrfNoMap::containsKey)
                                        .map(sgsTrfNoMap::get)
                                        .orElse("")
                        )
                );
        BoundTrfInfoSearchReq deepCopy = JSON.parseObject(JSON.toJSONString(boundTrfInfoSearchReq), BoundTrfInfoSearchReq.class);
        deepCopy.clearQuery();
        deepCopy.setTrfNos(trfNos);
        deepCopy.setSearchType(SearchType.BoundListType);
        List<BoundTrfRelDTO> boundTrfInfoList = trfDomainService.getBoundTrfInfoList(deepCopy, ImmutableList.of(
                TrfStatusEnum.ToPrice.getStatus(),
                TrfStatusEnum.Invoiced.getStatus(),
                TrfStatusEnum.ToBeTested.getStatus(),
                TrfStatusEnum.Testing.getStatus(),
                TrfStatusEnum.Detected.getStatus(),
                TrfStatusEnum.Completed.getStatus(),
                com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.Revise.getStatus()
        ));
        if(!CollectionUtils.isEmpty(boundTrfInfoList)) {
            List<Object> trfDatas = this.dealResultForSearch(boundTrfInfoList, refSystem, null);

            ConvertDataReq reqData = new ConvertDataReq();
            // TODO SCI-900
            reqData.setRefSystemId(refSystem.getRefSystemId());
            if (Objects.equals(refSystem.getRefSystemId(), 2)) {
                reqData.setRefSystemId(1);
            }
            reqData.setCustomerGroupCode(data.getCustomerGroupCode());
//        reqData.setProductLineId(data.getProductLineId());
            reqData.setSceneTypes(Lists.newArrayList(SearchType.BoundListType.getDesc()));

            // 请求 iLayer 获取 新结构
            JsonNode jsonNode = iLayerClient.convertData(reqData, trfDatas);

            if (Func.isEmpty(jsonNode)) {
                throw new IllegalStateException("获取TRF信息失败，请检查数据！");
            }

            JsonNode jsonNodeRsp = jsonNode.get(SearchType.BoundListType.getDesc());
            if (jsonNodeRsp == null || !jsonNodeRsp.isArray()) {
                throw new IllegalStateException("请检查数据mapping配置（TO 开发）");
            }

            Stream<JsonNode> boundListStream = StreamSupport.stream(
                    Spliterators.spliteratorUnknownSize(jsonNodeRsp.iterator(), Spliterator.ORDERED),
                    false // false表示非并行流
            );

            Map<String, JsonNode> boundMap = boundListStream.filter(Objects::nonNull)
                    .filter(node -> Objects.nonNull(node.get("trfNo")))
                    .collect(Collectors.toMap(node -> node.get("trfNo").asText(), Function.identity(), (v1, v2) -> v1));

            FastJSONUtils.toJSONObjectStream(dataJSONArray)
                    .filter(item -> boundMap.containsKey(item.getString("RequestNo")))
                    .forEach(item -> item.put("boundInfo", boundMap.get(item.getString("RequestNo"))));
        }
    }

    /**
     * 导出接口
     *
     * @param reqObject
     * @return
     */
    public CustomResult exportTrfInfoList(JsonNode reqObject) {
        CustomResult rspResult = new CustomResult();
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String currentLabCode = user.getCurrentLabCode();
        CustomResult<TodoListBaseDataDTO> jsonDataRsp = this.checkJsonNode(reqObject);
        if (!jsonDataRsp.isSuccess()) {
            return rspResult.fail(jsonDataRsp.getMsg());
        }

        TodoListBaseDataDTO data = jsonDataRsp.getData();
        Map<String, List<String>> allRequestMaps = data.getAllRequestMaps();
        int productLineId = data.getProductLineId();
        SearchType searchType = data.getSearchType();
        String orderNo = data.getOrderNo();
        String trfNo = data.getTrfNo();
        String customerGroupCode = data.getCustomerGroupCode();
        String packageBarcode = data.getPackageBarcode();
        String trfStartDate = data.getTrfStartDate();
        String trfEndDate = data.getTrfEndDate();
        String createDate = data.getCreateDate();
        String endDate = data.getEndDate();
        List<String> createBys = data.getCreateBys();
        String exportSceneType = searchType.getExportDesc();
        Boolean createMoreOrder = JsonNodeUtils.getBooleanValueFromJson(reqObject, CREATE_MORE_ORDER);

        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(data.getRefSystemId());
        if (refSystem == null) {
            return rspResult.fail("Please Check customerGroupCode！");
        }
        // 拼装参数时 需要排除掉 创建时间 创建人
        allRequestMaps.remove(CREATED_DATE);
        allRequestMaps.remove(CREATED_BY);
        allRequestMaps.remove(TRF_SUBMISSION_DATE);
        // 搜索时不需要在Content中搜索TRF_NO OrderNo
        allRequestMaps.remove(TRF_NO);
        allRequestMaps.remove(PACKAGE_BARCODE);
        allRequestMaps.remove(ORDER_NO);

        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        if (StringUtils.isNotBlank(trfNo)) {
            searchTrfInfoDTO.setTrfNos(Lists.newArrayList(trfNo));
        }
        if (StringUtils.isNotBlank(orderNo)) {
            searchTrfInfoDTO.setOrderNo(orderNo);
        }
        searchTrfInfoDTO.setLabCode(currentLabCode);
        if(todoListParamConfig.isEnableTrfSubmissionDate()) {
            searchTrfInfoDTO.setTrfStartDate(trfStartDate);
            searchTrfInfoDTO.setTrfEndDate(trfEndDate);
        }
        searchTrfInfoDTO.setCreateDate(createDate);
        searchTrfInfoDTO.setEndDate(endDate);
        searchTrfInfoDTO.setCreateBys(createBys);
        searchTrfInfoDTO.setRequestMaps(allRequestMaps);
        searchTrfInfoDTO.setRefSystemId(refSystem.getRefSystemId());
        if (StringUtils.isNotBlank(packageBarcode)) {
            searchTrfInfoDTO.setPackageBarcodes(Lists.newArrayList(packageBarcode));
        }
        // HL 希音供应商查询 特殊点
        if (ProductLineType.check(productLineId, ProductLineType.HL) &&
                RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier)
        ) {
            searchTrfInfoDTO.setHasConnect(true);
        }

        // 取记录总数，复用list接口，避免拼接条件
        int total = getExportTrfTotal(searchTrfInfoDTO, createMoreOrder, searchType);
        if (total == 0) {
            return rspResult.fail("没有符合条件的数据！");
        }
        if (total > 10000) {
            return rspResult.fail("The maximum export data is 10000, please shorten the export time");
        }

        //根据场景取得excel格式
        TrfTodoReq trfTodoReq = new TrfTodoReq();
        trfTodoReq.setRefSystemId(refSystem.getRefSystemId());
        trfTodoReq.setCustomerGroupCode(customerGroupCode);
        trfTodoReq.setSceneTypes(Lists.newArrayList(exportSceneType));
        ConcurrentHashMap<String, JsonNode> showSceneList = customerSceneCacheService.getCustomerSceneList(trfTodoReq, productLineId);
        if (showSceneList.isEmpty()) {
            return CustomResult.newSuccessInstance();
        }
        JsonNode showNode = showSceneList.get(exportSceneType);

        //设置导出excel结构
        List<ExcelCommonInfo> commonInfoList = new ArrayList<>();
        ExcelCommonInfo excelCommonInfo = buildExcelFormat(showNode);
        //初始化数据list，用于传入多线程添加数据
        List<Map<String, String>> mapList = new ArrayList<>();
        excelCommonInfo.setDataList(mapList);
        commonInfoList.add(excelCommonInfo);

        //创建线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(QUEUE_CAPACITY),
                new ThreadPoolExecutor.CallerRunsPolicy());

        //分页处理数据，每页100条
        int rows = 100;
        int pages = (int) (Math.ceil((double) total / rows));
        for (int pageIdx = 0; pageIdx < pages; pageIdx++) {

            int finalPageIdx = pageIdx + 1;
            executor.execute(() -> {
                try {
                    asyncbuildExcelInfo(mapList, searchTrfInfoDTO, jsonDataRsp, showNode
                            , createMoreOrder, finalPageIdx, rows);
                } catch (Exception e) {
                    logger.error("TodoListService_asyncbuildExcelInfo执行异常{}", e.getMessage(), e);
                    //终止线程池
                    executor.shutdown();
                }
            });
        }

        //阻塞等待线程结束
        executor.shutdown();
        while (!executor.isTerminated()) {
            try {
                Thread.sleep(1000);
            } catch (Exception e) {
                logger.error("TodoListService_executor error", e);
            }
        }

        logger.info("TodoListService_exportTrfInfoList mapList.size={}", mapList.size());
        if (mapList.size() != total) {
            logger.error("TodoListService_exportTrfInfoList mapList.size={}", mapList.size());
            return rspResult.fail("服务器出了点小差错，请稍后再试.");
        }

        Collections.sort(mapList, (o1, o2) -> {
            // 假设createdDate是一个long类型字段
            if (o1.containsKey("createdDate") && o2.containsKey("createdDate")) {
                String createdDate1 = o1.get("createdDate");
                String createdDate2 = o2.get("createdDate");
                if (StringUtil.isNotEmpty(createdDate1) && StringUtil.isNotEmpty(createdDate2)) {
                    return DateUtils.parseDate(createdDate2).compareTo(DateUtils.parseDate(createdDate1)); // 倒序排序
                } else {
                    return ZERO;
                }
            } else {
                return ZERO;
            }
        });

        // 导出EXCEL
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            CustomResult sendDataDetail = ExcelUtil.exportXls(((ServletRequestAttributes) requestAttributes).getResponse(), commonInfoList,
                    String.format(searchType.check(SearchType.ToDoListType) ? "todo_list_%s" : "bound_list_%s", DateUtils.format(DateTime.now().toDate())));
            logger.info("excel 结果：{}", sendDataDetail.isSuccess());
        }

        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 异步执行导出excel数据处理
     *
     * @param mapList          excel数据载体实体
     * @param searchTrfInfoDTO 查询条件
     * @param jsonDataRsp      接口请求参数
     * @param showNode         excel页头
     * @param createMoreOrder  是否查询已绑定订单
     * @param pageIdx          页码
     * @param rows             每页行数
     * @return void
     */
    private void asyncbuildExcelInfo(List<Map<String, String>> mapList
            , SearchTrfInfoDTO searchTrfInfoDTO
            , CustomResult<TodoListBaseDataDTO> jsonDataRsp
            , JsonNode showNode
            , Boolean createMoreOrder, int pageIdx, int rows) {

        logger.info("TodoListService_asyncbuildExcelInfo 执行开始 执行页码{}", pageIdx);
        TodoListBaseDataDTO data = jsonDataRsp.getData();
        String trfNo = data.getTrfNo();
        int productLineId = data.getProductLineId();
        String customerGroupCode = data.getCustomerGroupCode();
        SearchType searchType = data.getSearchType();
        String exportSceneType = searchType.getExportDesc();
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(data.getRefSystemId());

        //分页查询
        BoundTrfInfoSearchReq boundTrfInfoSearchReq = new BoundTrfInfoSearchReq();
        BeanUtils.copyProperties(searchTrfInfoDTO, boundTrfInfoSearchReq);
        boundTrfInfoSearchReq.setCreateMoreOrder(createMoreOrder);
        boundTrfInfoSearchReq.setSearchType(searchType);
        boundTrfInfoSearchReq.setPage(pageIdx);
        boundTrfInfoSearchReq.setRows(rows);
        logger.info("TodoListService_getBoundTrfInfoList 执行开始 执行页码{}", pageIdx);
        List<BoundTrfRelDTO> boundTrfRelDTOS = trfDomainService.getBoundTrfInfoList(boundTrfInfoSearchReq);
        logger.info("TodoListService_getBoundTrfInfoList 执行结束 执行页码{}", pageIdx);

        // 结果处理
        List<Object> trfDatas = this.dealResultForSearch(boundTrfRelDTOS, refSystem, trfNo);

        // 请求 iLayer 获取 新结构
        ConvertDataReq reqData = new ConvertDataReq();
        // TODO SCI-900
        reqData.setRefSystemId(data.getRefSystemId());
        reqData.setCustomerGroupCode(customerGroupCode);
        maybeRewriteRefSystemId(data.getRefSystemId(), reqData);
        reqData.setProductLineId(productLineId);
        reqData.setSceneTypes(Lists.newArrayList(exportSceneType));
        //允许失败重试一次
        JsonNode jsonNode = null;
        int times = 0;
        int maxTimes = 2;
        while (times < maxTimes) {
            times++;
            jsonNode = iLayerClient.convertData(reqData, trfDatas);
            if (Func.isNotEmpty(jsonNode)) {
                break;
            }
            if (Func.isEmpty(jsonNode) && times == maxTimes - 1) {
                throw new BizException("获取TRF信息失败，请检查数据！");
            }
        }

        JsonNode jsonNodeRsp = jsonNode.get(exportSceneType);
        synchronized (this) {
            logger.info("TodoListService_buildExcelDataList 执行开始 执行页码{}", pageIdx);
            buildExcelDataList(mapList, jsonNodeRsp, showNode);
            logger.info("TodoListService_buildExcelDataList 执行结束 执行页码{}", pageIdx);
        }

        logger.info("TodoListService_asyncbuildExcelInfo 执行结束 执行页码{}", pageIdx);
    }

    /**
     * 设置excel导出格式
     *
     * @param fieldNodes 字段
     * @return ExcelCommonInfo excel格式
     */
    private ExcelCommonInfo buildExcelFormat(JsonNode fieldNodes) {
        ExcelCommonInfo excelCommonInfo = new ExcelCommonInfo();
        excelCommonInfo.setSheetName("data");
        excelCommonInfo.setSheetIndex(1);
        List<ExcelDataConfigInfo> excelDataConfigInfoList = new ArrayList<>();
        for (JsonNode fieldNode : fieldNodes) {
            String key = fieldNode.get(FIELD_CODE).textValue();
            ExcelDataConfigInfo excelDataConfigInfo = new ExcelDataConfigInfo();
            excelDataConfigInfo.setIndex(Integer.valueOf(fieldNode.get(FIELD_SEQ).textValue()));
            excelDataConfigInfo.setWidth(fieldNode.get(WIDTH).intValue());
            excelDataConfigInfo.setFieldName(key);
            excelDataConfigInfo.setTitle(fieldNode.get(DISPLAY_NAME).textValue());
            excelDataConfigInfoList.add(excelDataConfigInfo);
        }
        excelCommonInfo.setDataConfig(excelDataConfigInfoList);

        return excelCommonInfo;
    }

    /**
     * 设置导出 数据
     *
     * @param dataMaps   保持excel数据列表
     * @param dataNode   数据
     * @param fieldNodes 字段
     * @return void
     */
    private void buildExcelDataList(List<Map<String, String>> dataMaps, JsonNode dataNode, JsonNode fieldNodes) {

        if (dataNode.isArray()) {
            Iterator<JsonNode> it = dataNode.iterator();
            while (it.hasNext()) {
                JsonNode childNode = it.next();
                Map<String, String> map = new HashMap<>();
                for (JsonNode fieldNode : fieldNodes) {
                    String key = fieldNode.get(FIELD_CODE).textValue();
                    JsonNode jsonNode = childNode.get(key);
                    String value = null;
                    if (jsonNode != null) {
                        value = jsonNode.textValue();
                    }
                    map.put(key, value);
                }
                dataMaps.add(map);
            }
        }
    }

    /**
     * 取得导出trf报表总记录数
     *
     * @param searchTrfInfoDTO 查询条件
     * @param createPageOrder  是否绑定订单
     * @param searchType
     * @return 数量
     */
    private int getExportTrfTotal(SearchTrfInfoDTO searchTrfInfoDTO, Boolean createPageOrder, SearchType searchType) {
        List<BoundTrfRelDTO> pageCountTOS = Lists.newArrayList();
        BoundTrfInfoSearchReq pageCountReq = new BoundTrfInfoSearchReq();
        BeanUtils.copyProperties(searchTrfInfoDTO, pageCountReq);
        pageCountReq.setCreateMoreOrder(createPageOrder);
        pageCountReq.setSearchType(searchType);
        pageCountReq.setPage(1);
        pageCountReq.setRows(1);
        pageCountTOS = trfDomainService.getBoundTrfInfoList(pageCountReq);

        PageInfo<BoundTrfRelDTO> pageTestLine = PageInfo.of(pageCountTOS);
        return Math.toIntExact(pageTestLine.getTotal());
    }

    /**
     * 搜索的结果 添加部分业务数据后 再发送给iLayer
     *
     * @param boundTrfRelDTOS
     * @param refSystem
     * @return
     */
    private List<Object> dealResultForSearch(List<BoundTrfRelDTO> boundTrfRelDTOS, RefSystemIdEnum refSystem, String trfNo) {
        return dealResultForSearch(boundTrfRelDTOS, refSystem, trfNo, true);
    }

    /**
     * 搜索的结果 添加部分业务数据后 再发送给iLayer
     *
     * @param boundTrfRelDTOS
     * @param refSystem
     * @return
     */
    private List<Object> dealResultForSearch(List<BoundTrfRelDTO> boundTrfRelDTOS, RefSystemIdEnum refSystem, String trfNo, boolean hasChild) {
        List<Object> trfDatas = Lists.newArrayList();
        for (BoundTrfRelDTO trfInfo : boundTrfRelDTOS) {
            if (StringUtils.isEmpty(trfInfo.getContent())) {
                continue;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode jsonNode = objectMapper.readTree(trfInfo.getContent());
                ObjectNode objectNode = jsonNode.deepCopy();
//                order created by
                if (trfInfo.getCreatedBy() != null) {
                    objectNode.put(CREATED_BY, trfInfo.getCreatedBy());
                }
//                order created when
                if (trfInfo.getCreatedDate() != null) {
                    objectNode.put(CREATED_DATE, DateUtils.format(trfInfo.getCreatedDate()));
                }
                objectNode.put(TRF_STATE, trfInfo.getTrfStatus());
                switch (refSystem) {
                    case Shein:
                        objectNode.put(TRF_ID, trfInfo.getId());
                        objectNode.put(ORDER_NO, trfInfo.getOrderNo());
                        objectNode.put(GENERAL_ORDER_ID, trfInfo.getOrderId());
                        objectNode.put(TRF_STATUS, trfInfo.getTrfStatus());
                        objectNode.put(CANCELLED_BY, StringUtils.defaultIfBlank(trfInfo.getCancelBy(), ""));
                        objectNode.put(CANCELLED_DATE, trfInfo.getCancelDate() == null ? "" : trfInfo.getCancelDate());
                        objectNode.put(CANCELLED_REASON, StringUtils.defaultIfBlank(trfInfo.getCancelReason(), ""));
                        objectNode.put(CANCELLED_REASON_CONTENT, StringUtils.defaultIfBlank(trfInfo.getCancelReasonContent(), ""));
                        JsonNode picturesNode = objectNode.get(PICTURES);
                        ObjectNode pictureIdsObjectItem = objectMapper.createObjectNode();
                        pictureIdsObjectItem.put(PICTURE_ID, UUID.randomUUID().toString());
                        List<ObjectNode> pictureIdsObjectNodes = Lists.newArrayList();
                        if (Func.isNotEmpty(picturesNode) && picturesNode.isArray()) {
                            Iterator<JsonNode> it = picturesNode.iterator();
                            while (it.hasNext()) {
                                JsonNode next = it.next();
                                JsonNode node = next.get(PICTURE_ID);
                                pictureIdsObjectNodes.add(node.deepCopy());
                            }
                        } else {
                            JsonNode node = objectNode.get(PICTURE_ID);
                            if (Func.isNotEmpty(node)) {
                                pictureIdsObjectNodes.add(node.deepCopy());
                            }
                        }
                        objectNode.put(PICTURE_IDS, objectMapper.readTree(pictureIdsObjectNodes.toString()));

                        JsonNode goodsPictureUrlListNode = objectNode.get(GOODS_PICTURE_URL_LIST);
                        if (Func.isEmpty(goodsPictureUrlListNode)) {
                            if (goodsPictureUrlListNode == null) {
                                List<ObjectNode> objectNodes = Lists.newArrayList();
                                JsonNode node = objectNode.get(GOODS_PICTURE_URL);
                                if (Func.isNotEmpty(node)) {
                                    objectNodes.add(node.deepCopy());
                                }
                                objectNode.put(GOODS_PICTURE_URL_LIST, objectMapper.readTree(objectNodes.toString()));
                            }
                        }
                        if (Func.isEmpty(objectNode.get(GOODS_COLOR))) {
                            objectNode.put(GOODS_COLOR, "");
                        } else {
                            objectNode.put(GOODS_COLOR, objectNode.get(GOODS_COLOR));
                        }
                        break;
                    case ANTA:
                        JsonNode antaOrderInfo = objectNode.get(refSystem.getSampleNode());
                        if (antaOrderInfo != null && antaOrderInfo.isArray()) {
                            Iterator<JsonNode> it = antaOrderInfo.iterator();
                            List<ObjectNode> objectNodes = Lists.newArrayList();
                            while (it.hasNext()) {
                                JsonNode childNode = it.next();
                                ObjectNode objectItem = childNode.deepCopy();
                                objectItem.put(TRF_ID, trfInfo.getId());
                                objectItem.put(ORDER_NO, trfInfo.getOrderNo());
                                objectItem.put(GENERAL_ORDER_ID, trfInfo.getOrderId());
                                objectNodes.add(objectItem);
                            }
                            objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                        }
                        break;
                    case TARGET_INSPECTORIO:
                        objectNode.put(TRF_ID, trfInfo.getId());
                        objectNode.put(TRF_NO, trfInfo.getTrfNo());
                        objectNode.put(SGS_TRF_NO, trfInfo.getSgsTrfNo());
                        objectNode.put(ORDER_NO, trfInfo.getOrderNo());
                        objectNode.put(GENERAL_ORDER_ID, trfInfo.getOrderId());
                        objectNode.put(TRF_STATUS, trfInfo.getTrfStatus());
                        objectNode.put(CANCELLED_BY, StringUtils.defaultIfBlank(trfInfo.getCancelBy(), ""));
                        objectNode.put(CANCELLED_DATE, trfInfo.getCancelDate() == null ? "" : trfInfo.getCancelDate());
                        objectNode.put(CANCELLED_REASON, StringUtils.defaultIfBlank(trfInfo.getCancelReason(), ""));
                        objectNode.put(CANCELLED_REASON_CONTENT, StringUtils.defaultIfBlank(trfInfo.getCancelReasonContent(), ""));
                        objectNode.put(EnquiryNo, StringUtils.defaultIfBlank(trfInfo.getEnquiryNo(), ""));
                        break;
                    case SheinSupplier:
                    case LINING:
                    case Camel:
                    case PeaceBird:
                    case SGSMart:
                    case F21:
                    case JO_ANN:
                    case Walmart:
                    case Walmart_Group:
                    case Target:
                    case BigLots:
                    case DollarTree:
                    case Veyer:
                    case Amazon:
                    case SEMIR:
                    case FastFish:
                    case LOWES:
                    case Septwolves:
                    case TIC:
                        objectNode.put(TRF_ID, trfInfo.getId());
                        objectNode.put(ORDER_NO, trfInfo.getOrderNo());
                        objectNode.put(GENERAL_ORDER_ID, trfInfo.getOrderId());
                        objectNode.put(TRF_STATUS, trfInfo.getTrfStatus());
                        objectNode.put(CANCELLED_BY, StringUtils.defaultIfBlank(trfInfo.getCancelBy(), ""));
                        objectNode.put(CANCELLED_DATE, trfInfo.getCancelDate() == null ? "" : trfInfo.getCancelDate());
                        objectNode.put(CANCELLED_REASON, StringUtils.defaultIfBlank(trfInfo.getCancelReason(), ""));
                        objectNode.put(CANCELLED_REASON_CONTENT, StringUtils.defaultIfBlank(trfInfo.getCancelReasonContent(), ""));
                        objectNode.put(EnquiryNo, StringUtils.defaultIfBlank(trfInfo.getEnquiryNo(), ""));
                        break;
                }

                // 组装childOrderList
                if (hasChild) {
                    String orderNo = trfInfo.getOrderNo();
                    if (Func.isNotEmpty(orderNo)) {
                        List<Map<String, String>> childOrderList = trfOrderDomainService.selectChildOrderListByOrderNo(orderNo, trfInfo.getSystemId());
                        if (Func.isNotEmpty(childOrderList)) {
                            objectNode.put(CHILD_ORDER_LIST, objectMapper.readTree(JSONObject.toJSONString(childOrderList)));
                        }
                    }

                    trfDatas.add(JSON.parseObject(objectNode.toString()));
                }
            } catch (Exception ex) {
                logger.error("trfNo_{},Content 转换失败，Error：{}", trfNo, ex);
            }
        }
        return trfDatas;
    }

    /**
     * 设置导出 格式
     *
     * @param dataNode       数据
     * @param commonInfoList 导出格式
     * @param fieldNodes     字段
     * @return 返回响应
     */
    public List<Map<String, String>> buildResultData(JsonNode dataNode, List<ExcelCommonInfo> commonInfoList, JsonNode fieldNodes) {
        ExcelCommonInfo excelCommonInfo = new ExcelCommonInfo();
        excelCommonInfo.setSheetName("data");
        excelCommonInfo.setSheetIndex(1);
        List<ExcelDataConfigInfo> excelDataConfigInfoList = new ArrayList<>();
        for (JsonNode fieldNode : fieldNodes) {
            String key = fieldNode.get(FIELD_CODE).textValue();
            ExcelDataConfigInfo excelDataConfigInfo = new ExcelDataConfigInfo();
            excelDataConfigInfo.setIndex(Integer.valueOf(fieldNode.get(FIELD_SEQ).textValue()));
            excelDataConfigInfo.setWidth(fieldNode.get(WIDTH).intValue());
            excelDataConfigInfo.setFieldName(key);
            excelDataConfigInfo.setTitle(fieldNode.get(DISPLAY_NAME).textValue());
            excelDataConfigInfoList.add(excelDataConfigInfo);
        }
        excelCommonInfo.setDataConfig(excelDataConfigInfoList);

        List<Map<String, String>> dataMaps = new ArrayList<>();
        if (dataNode.isArray()) {
            Iterator<JsonNode> it = dataNode.iterator();
            while (it.hasNext()) {
                JsonNode childNode = it.next();
                Map<String, String> map = new HashMap<>();
                for (JsonNode fieldNode : fieldNodes) {
                    String key = fieldNode.get(FIELD_CODE).textValue();
                    JsonNode jsonNode = childNode.get(key);
                    String value = null;
                    if (jsonNode != null) {
                        value = jsonNode.textValue();
                    }
                    map.put(key, value);
                }
                dataMaps.add(map);
            }
        }
        excelCommonInfo.setDataList(dataMaps);

        commonInfoList.add(excelCommonInfo);
        return dataMaps;
    }

    /**
     * trf 详情接口
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTrfDetailInfo(TrfDetailReq reqObject) {
        CustomResult rspResult = getCustomizedTrfDetailData(reqObject);
        if (!rspResult.isSuccess()) {
            return rspResult;
        } else {
            ObjectNode objectNode = this.getObjectNode((JsonNode) rspResult.getData());
            if (objectNode != null) {
                objectNode.set(SCENE_TYPES, this.getSceneTypeList());
            }
            rspResult.setData(objectNode);
            rspResult.setSuccess(true);
            return rspResult;
        }
    }


    public CustomResult getCustomerTrfDetailInfo(TrfDetailReq reqObject) {
        if (CollectionUtils.isEmpty(reqObject.getTrfNo())) {
            throw new IllegalArgumentException("trfNo must be not empty");
        }
        CustomResult rspResult = new CustomResult();
        SystemApiDTO importApiInfo = systemAPIConfigService.getImportApiInfo(reqObject.getRefSystemId());
        if (Func.isEmpty(importApiInfo)) {
            return rspResult;
        }
        String requestBodyTemplate = importApiInfo.getRequestBodyTemplate();
        Map<String, String> reqPramas = ImmutableMap.of(
                "trfNo", reqObject.getTrfNo().get(0),
                "productLineCode", reqObject.getProductLineCode()
        );
        BaseResponse baseResponse = iLayerClient.syncGetInfo(jsonDataConvertor.convert(JSONObject.toJSONString(reqPramas), requestBodyTemplate));
        if (baseResponse.isSuccess()) {
            rspResult.setSuccess(true);
            rspResult.setData(baseResponse.getData());
            return rspResult;
        } else {
            ObjectNode objectNode = this.getObjectNode((JsonNode) rspResult.getData());
            if (objectNode != null) {
                objectNode.set(SCENE_TYPES, this.getSceneTypeList());
            }
            rspResult.setData(objectNode);
            rspResult.setSuccess(true);
            return rspResult;
        }
    }


    /**
     * @param trf
     * @param objectMapper
     * @param objectNode
     * @param refSystem
     * @param sampleNoIndex
     * @throws Exception
     */
    private void resetObjectNode(CustomerTrfInfoRsp trf, ObjectMapper objectMapper, ObjectNode objectNode, RefSystemIdEnum refSystem, Integer sampleNoIndex) throws Exception {
        String fieldName = refSystem.getSampleNode();
        JsonNode jsonNode = objectNode.get(fieldName);
        if (jsonNode != null && jsonNode.isArray()) {
            Iterator<JsonNode> it = jsonNode.iterator();
            List<ObjectNode> objectNodes = Lists.newArrayList();
            while (it.hasNext()) {
                JsonNode childNode = it.next();
                ObjectNode objectItem = childNode.deepCopy();
                switch (refSystem) {
                    case SheinSupplier:
                        // 不需要添加 sampleNo
                        //  一个trfNo 对应对个sample时，需要在此处理sampleNo
                        objectItem.put(SAMPLE_HIDE, NumberUtil.numberToLetter(sampleNoIndex));
                        sampleNoIndex++;
                        break;
                    case ANTA:
                        objectItem.put(SAMPLE_HIDE, NumberUtil.numberToLetter(sampleNoIndex));
                        objectItem.put(SAMPLE_NO, NumberUtil.numberToLetter(sampleNoIndex));
                        sampleNoIndex++;
                        break;
                    case TIC:
                        break;
                    default:
                        objectItem.put(SAMPLE_NO, NumberUtil.numberToLetter(sampleNoIndex));
                        break;
                }
                objectNodes.add(objectItem);
            }
            objectNode.set(fieldName, objectMapper.readTree(objectNodes.toString()));
        }
        trf.setContent(objectNode.toString());
    }

    /**
     * @param rootNode
     * @return
     */
    public ObjectNode getObjectNode(JsonNode rootNode) {
        ObjectNode objectNode = new ObjectMapper().createObjectNode();

        // 遍历某个JsonNode的key和value(value可能是字符串也可能是子jsonNode，但如果value是jsonNode数组的话，是无法读取的)
        Iterator<Map.Entry<String, JsonNode>> jsonNodes = rootNode.fields();
        int jsonIndex = 10;
        while (jsonNodes.hasNext()) {
            Map.Entry<String, JsonNode> entry = jsonNodes.next();
            JsonNode jsonNode = entry.getValue();
            if (jsonNode == null || StringUtils.endsWithIgnoreCase(entry.getKey(), "_hide") || StringUtils.endsWithIgnoreCase(entry.getKey(), "_checkbox")) {
                if (StringUtils.equalsIgnoreCase(SHOW_PIC_HIDE, entry.getKey())) {
                    continue;
                }
                objectNode.set(entry.getKey(), jsonNode);
                continue;
            }
            if (!(jsonNode.isObject() || jsonNode.isArray())) {
                String objectKey = String.format("%s#%s", jsonIndex++, entry.getKey());
                objectNode.set(objectKey, jsonNode);
                if (StringUtils.equalsIgnoreCase(entry.getKey(), GOODS_PICTURE)) {
                    objectNode.set(SHOW_PIC_HIDE, new TextNode(objectKey));
                }
                continue;
            }
            if (!jsonNode.isArray()) {
                objectNode.set(entry.getKey(), jsonNode);
                continue;
            }
            /*List<JsonNode> itemNames = jsonNode.findValues("itemName");
            for(JsonNode itemName: itemNames){
                System.out.println(itemName.toString());
            }*/
            objectNode.set(entry.getKey(), this.getArrayNode(jsonNode));
        }
        return objectNode;
    }

    /**
     * @param arrayNode
     * @return
     */
    public ArrayNode getArrayNode(JsonNode arrayNode) {
        if (arrayNode == null || arrayNode.isEmpty() || !arrayNode.isArray()) {
            return null;
        }
        ArrayNode arrayNodes = new ObjectMapper().createArrayNode();
        Iterator<JsonNode> jsonNodes = arrayNode.iterator();
        while (jsonNodes.hasNext()) {
            JsonNode jsonNode = jsonNodes.next();
            /*boolean isObject = jsonNode.isObject();
            boolean isArray = jsonNode.isArray();
            boolean isTextual = jsonNode.isTextual();
            boolean isContainerNode = jsonNode.isContainerNode();
            boolean isMissingNode = jsonNode.isMissingNode();
            boolean isValueNode = jsonNode.isValueNode();*/
            if (!jsonNode.isObject()) {
                arrayNodes.add(jsonNode);
                continue;
            }
            arrayNodes.add(this.getObjectNode(jsonNode));
        }
        return arrayNodes;
    }

    /**
     * @return
     */
    private JsonNode getSceneTypeList() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<SceneTypeInfo> sceneTypes = Lists.newArrayList();

        SceneTypeInfo sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_general");
        sceneType.setSceneName("General Info");
        sceneType.setSceneSeq(0);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_sample_detail");
        sceneType.setSceneName("Sample Detail List");
        sceneType.setSceneSeq(1);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_customer");
        sceneType.setSceneName("Customer List");
        sceneType.setSceneSeq(2);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_product");
        sceneType.setSceneName("Product & Sample List");
        sceneType.setSceneSeq(3);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_picture");
        sceneType.setSceneName("Picture List");
        sceneType.setSceneSeq(4);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_checkitem");
        sceneType.setSceneName("Check Item List");
        sceneType.setSceneSeq(5);
        sceneTypes.add(sceneType);

        sceneType = new SceneTypeInfo();
        sceneType.setSceneType("trf_detail_attachment");
        sceneType.setSceneName("Attachment List");
        sceneType.setSceneSeq(6);
        sceneTypes.add(sceneType);

        try {
            return objectMapper.readTree(JSONObject.toJSONString(sceneTypes));
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * 通用校验
     *
     * @param reqObject
     * @return
     */
    public CustomResult<TodoListBaseDataDTO> checkJsonNode(JsonNode reqObject) {
        CustomResult<TodoListBaseDataDTO> rspResult = new CustomResult();
        if (reqObject == null) {
            return rspResult.fail("Please check the parameters!");
        }
        TodoListBaseDataDTO baseData = new TodoListBaseDataDTO();
        JsonNode customerGroupCode = reqObject.get(CUSTOMER_GROUP_CODE);
        if (customerGroupCode != null && StringUtils.isNotBlank(customerGroupCode.textValue())) {
            baseData.setCustomerGroupCode(customerGroupCode.textValue());
        }

        Integer productLineId = ProductLineContextHolder.getProductLineId();
        if (productLineId == null || productLineId.intValue() <= 0) {
            return rspResult.fail("Please check the productLineCode！");
        }
        baseData.setProductLineId(productLineId);

        // 解析 sceneType
        List<String> sceneTypes = JsonNodeUtils.getJsonNodeList(reqObject, SCENE_TYPES);
        if (CollectionUtils.isEmpty(sceneTypes)) {
            return rspResult.fail("Please check the sceneType！");
        }
        baseData.setSceneTypes(sceneTypes);

        // 解析 refSystemId
        baseData.setRefSystemId(NumberUtil.toInt(JsonNodeUtils.getValueFromJson(reqObject, REF_SYSTEM_ID)));
        // 解析 batchNo
        baseData.setBatchNo(JsonNodeUtils.getValueFromJson(reqObject, BATCH_NO));
        // 解析 trfNo
        baseData.setTrfNo(JsonNodeUtils.getValueFromJson(reqObject, TRF_NO));
        // 解析 PACKAGE_BARCODE
        baseData.setPackageBarcode(JsonNodeUtils.getValueFromJson(reqObject, PACKAGE_BARCODE));
        // 解析 ORDER_NO
        baseData.setOrderNo(JsonNodeUtils.getValueFromJson(reqObject, ORDER_NO));
        // 解析 SEARCH_TYPE
        String searchType = JsonNodeUtils.getValueFromJson(reqObject, SEARCH_TYPE);
        // 解析 STATUS
        baseData.setStatus(JsonNodeUtils.getValueFromJson(reqObject, TRF_STATUS));
        if (Func.isBlank(baseData.getStatus())) {
            baseData.setStatus(JsonNodeUtils.getValueFromJson(reqObject, STATUS));
        }
        baseData.setSearchType(SearchType.defaultCheck(NumberUtil.toInt(searchType), SearchType.ToDoListType));

        // 获取请求 trf_todo_list_filter 的所有 属性值 优先使用 sceneTypes
        // TODO 临时处理 SCI-900
        if (baseData.getRefSystemId() == 2) {
            baseData.setRefSystemId(1);
        }
        TrfTodoReq trfTodoReq = new TrfTodoReq();
        trfTodoReq.setRefSystemId(baseData.getRefSystemId());
        trfTodoReq.setCustomerGroupCode(baseData.getCustomerGroupCode());
        trfTodoReq.setSceneTypes(sceneTypes);
        ConcurrentHashMap<String, JsonNode> customerSceneList = customerSceneCacheService.getCustomerSceneList(trfTodoReq, baseData.getProductLineId());

        if (customerSceneList.isEmpty()) {
            return rspResult.fail("Unable to get data！customerGroupCode：" + baseData.getCustomerGroupCode());
        }
        baseData.setCustomerSceneList(customerSceneList);

        ConcurrentHashMap<String, Map<String, List<String>>> requestMaps = this.getRequestMaps(reqObject, customerSceneList);
        if (requestMaps.isEmpty()) {
            return rspResult.fail("Please check the configuration parameters of the parameter page！");
        }
        baseData.setRequestMaps(requestMaps);

        Map<String, List<String>> allRequestMaps = Maps.newHashMap();
        for (String sceneItem : sceneTypes) {
            if (allRequestMaps.containsKey(sceneItem)) {
                continue;
            }
            allRequestMaps.putAll(requestMaps.get(sceneItem));
        }
        baseData.setAllRequestMaps(allRequestMaps);

        // 单个模块请求时  解析创建时间 创建人 等信息
        if (sceneTypes.size() == 1) {
            Map<String, List<String>> requestMapItem = requestMaps.get(sceneTypes.get(0));
            String createDate = StringUtils.EMPTY;
            String endDate = StringUtils.EMPTY;
            List<String> createBys = Lists.newArrayList();
            if (requestMapItem.containsKey(CREATED_DATE) && requestMapItem.get(CREATED_DATE).size() > 1) {
                createDate = requestMapItem.get(CREATED_DATE).get(0).trim() + " 00:00:00";
                endDate = requestMapItem.get(CREATED_DATE).get(1).trim() + " 23:59:59";
            }
            baseData.setCreateDate(createDate);
            baseData.setEndDate(endDate);

            String trfStartDate = StringUtils.EMPTY;
            String trfEndDate = StringUtils.EMPTY;
            if (requestMapItem.containsKey(TRF_SUBMISSION_DATE) && requestMapItem.get(TRF_SUBMISSION_DATE).size() > 1) {
                trfStartDate = requestMapItem.get(TRF_SUBMISSION_DATE).get(0).trim() + " 00:00:00";
                trfEndDate = requestMapItem.get(TRF_SUBMISSION_DATE).get(1).trim() + " 23:59:59";
                baseData.setTrfStartDate(trfStartDate);
                baseData.setTrfEndDate(trfEndDate);
            }


            if (requestMapItem.containsKey(CREATED_BY) && requestMapItem.get(CREATED_BY).size() > 0) {
                createBys = requestMapItem.get(CREATED_BY);
            }
            baseData.setCreateBys(createBys);
        }

        int page = 0;
        int rows = 0;
        JsonNode getPageNum = reqObject.get(PAGE);
        if (getPageNum != null && getPageNum.isInt()) {
            page = getPageNum.asInt();
        }

        JsonNode getPageSize = reqObject.get(ROWS);
        if (getPageSize != null && getPageSize.isInt()) {
            rows = getPageSize.asInt();
        }
        baseData.setPage(page);
        baseData.setRows(rows);

        // 执行系统没有修改，这个地方在处理完以后重新设置回2
        //TODO 临时处理 SCI-900
        if (baseData.getRefSystemId() == 1) {
            baseData.setRefSystemId(2);
        }

        rspResult.setData(baseData);
        rspResult.setSuccess(true);

        return rspResult;
    }


    /**
     * 根据入参 拼装参数
     *
     * @param reqObject   入参的jsonNode
     * @param filterNodes 请求所有模块的jsonNode
     * @return
     */
    private ConcurrentHashMap<String, Map<String, List<String>>> getRequestMaps(JsonNode reqObject, ConcurrentHashMap<String, JsonNode> filterNodes) {
        ConcurrentHashMap<String, Map<String, List<String>>> finalMap = new ConcurrentHashMap<>();

        if (filterNodes == null || filterNodes.isEmpty()) {
            return finalMap;
        }

        for (String filterKey : filterNodes.keySet()) {
            JsonNode filterNode = filterNodes.get(filterKey);
            if (filterNode == null) {
                continue;
            }
            Iterator<JsonNode> elements = filterNode.elements();
            Map<String, List<String>> requestMaps = Maps.newHashMap();

            // 查找 iLayer配置的属性值 在入参中有记录并且非空的，当做参数 查找数据库
            while (elements.hasNext()) {
                JsonNode node = elements.next();
                JsonNode fieldCode = node.findValue(FIELD_CODE);
                if (fieldCode == null) {
                    continue;
                }
                // 属性名
                String fieldName = fieldCode.asText();

                JsonNode element = reqObject.get(fieldName);
                if (element == null) {
                    continue;
                }
                if (StringUtils.isBlank(element.asText())) {
                    continue;
                }
                List<String> requestItems = Lists.newArrayList();
                JsonNode rule = node.findValue(RULES);

                if (rule == null || StringUtils.isEmpty(rule.asText())) {
                    requestItems.add(element.asText());
                } else {
                    requestItems.addAll(Lists.newArrayList(element.asText().split(rule.asText())));
                }
                // 属性转换
                JsonNode options = node.findValue(OPTIONS);
                if (options != null && StringUtils.isNotBlank(options.asText())) {
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(options.asText());
                        if (jsonObject.containsKey(TRANS)) {
                            fieldName = (String) jsonObject.get(TRANS);
                        }
                    } catch (Exception e) {
                        logger.error("请检查{}的配置：{}", fieldName, options);
                    }
                }

                requestMaps.put(fieldName, requestItems);
            }

            finalMap.put(filterKey, requestMaps);
        }

        return finalMap;
    }


    /**
     * 由于业务需要，需要将 希音返回的数据 做处理后再存库
     *
     * @param trfInfo
     */
    public void appendContentField(CustomerTrfInfoPO trfInfo) {
        if (StringUtils.isBlank(trfInfo.getContent())) {
            return;
        }
        String content = trfInfo.getContent();
        String trfNo = trfInfo.getTrfNo();
        String dffFormId = trfInfo.getDffFormId();
        String gridFormId = trfInfo.getGridFormId();

        ObjectMapper objectMapper = new ObjectMapper();

        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(trfInfo.getRefSystemId());
        if (refSystem == null) {
            return;
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(content);
            ObjectNode objectNode = jsonNode.deepCopy();
            JsonNode materialDetailList = objectNode.get(refSystem.getSampleNode());
            // 根据客户的数据添加相应的 属性
            switch (refSystem) {
                case Septwolves:
                    List<ObjectNode> pdfUrlList = Lists.newArrayList();
                    String pdfUrl = StringUtils.EMPTY;
                    JsonNode pdfUrlNode = objectNode.get(PDF_URL);
                    if (pdfUrlNode != null) {
                        String textValue = pdfUrlNode.asText();
                        if (!Objects.isNull(textValue)) {
                            pdfUrl = textValue;
                        }
                    }
                    if (StringUtils.isNotBlank(pdfUrl)) {
                        Map<String, FileInfo> fileInfoMap = fileClient.uploadTrfImageUrl(trfNo, pdfUrl);
                        ObjectNode pdfUrlObjectNode = objectMapper.createObjectNode();
                        pdfUrlObjectNode.put(FILE_ID, UUID.randomUUID().toString());
                        if (fileInfoMap != null && fileInfoMap.containsKey(trfNo)) {
                            // 设置fileId
                            pdfUrlObjectNode.put(FILE_ID, fileInfoMap.get(trfNo).getId());
                            pdfUrlObjectNode.put(CLOUD_ID, fileInfoMap.get(trfNo).getCloudID());
                            pdfUrlObjectNode.put(FILE_NAME, trfNo);
                        }
                        pdfUrlList.add(pdfUrlObjectNode);
                        objectNode.put(ATTACHMENTS, objectMapper.readTree(pdfUrlList.toString()));
                    }
                case Shein:
                case SheinSupplier:
                case LINING:
                case SGSMart:
                case F21:
                case JO_ANN:
                case Walmart:
                case Walmart_Group:
                case Target:
                case BigLots:
                case DollarTree:
                case LOWES:
                case TARGET_INSPECTORIO:
                case Veyer:
                case Amazon:
                case Camel:
                case PeaceBird:
                case SEMIR:
                case FastFish:
                case TIC:
                    if (refSystem == RefSystemIdEnum.TIC) {
                        objectNode.put(ORDER_NO_TIC, trfInfo.getTrfNo());
                        JsonNode sampleFormNode = objectNode.get(refSystem.getSampleNode());
                        if (sampleFormNode != null && sampleFormNode.isArray()) {
                            Iterator<JsonNode> it = sampleFormNode.iterator();
                            List<ObjectNode> objectNodes = Lists.newArrayList();
                            while (it.hasNext()) {
                                JsonNode childNode = it.next();
                                ObjectNode objectItem = childNode.deepCopy();
//                            objectItem.put(SAMPLE_NO, "");
                                objectItem.put(ID_CHECKBOX, "");
                                objectItem.put(LIST_ROW_ID_HIDE, objectItem.get(SAMPLE_NO));
                                objectItem.put(TRF_NO_HIDE, trfNo);
                                objectNodes.add(objectItem);
                            }
                            objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                        }
                    }
                    objectNode.put(PACKAGE_BARCODE, trfInfo.getObjectNo());
                    objectNode.put(BATCH_NO, trfInfo.getBatchNo());
                    objectNode.put(CREATED_DATE, DateUtils.formatDate(trfInfo.getCreatedDate()));
                    objectNode.put(CREATED_BY, trfInfo.getCreatedBy());
                    objectNode.put(LAB_TYPE, trfInfo.getLabCode());
                    objectNode.put(GROUP_ID_HIDE, 0);
                    objectNode.put(DFF_FORM_ID, dffFormId);
                    objectNode.put(GRID_FORM_ID, gridFormId);
                    objectNode.put(TRF_NO_HIDE, trfNo);
                    if (!RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier)) {
                        objectNode.put(LIST_ROW_ID_HIDE, UUID.randomUUID().toString());
                        objectNode.put(TRF_NO_HIDE, trfNo);
                        objectNode.put(ID_CHECKBOX, "");
                    }

                    JsonNode pictureUrlListNode = objectNode.get(GOODS_PICTURE_URL_LIST);
                    if (pictureUrlListNode != null && pictureUrlListNode.isArray()) {
                        Iterator<JsonNode> iterator = pictureUrlListNode.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (iterator.hasNext()) {
                            JsonNode childNode = iterator.next();
                            ObjectNode objectItem = objectMapper.createObjectNode();
                            String textValue = childNode.asText();
                            objectItem.put(ID_CHECKBOX, "");
                            objectItem.put(TRF_NO, trfNo);
                            objectItem.put(IMG_URL, textValue);
                            objectItem.put(PICTURE_ID, UUID.randomUUID().toString());
                            objectNodes.add(objectItem);
                        }
                        appendGoodsPictureList(objectNodes, objectNode, objectMapper);
                    }

                    JsonNode checkItemList = objectNode.get(CHECK_ITEM_LIST);
                    if (checkItemList != null && checkItemList.isArray()) {
                        Iterator<JsonNode> it = checkItemList.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            objectItem.put(GROUP_ID_HIDE, 0);
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(CHECK_ITEM_LIST, objectMapper.readTree(objectNodes.toString()));
                    }


                    if (materialDetailList != null && materialDetailList.isArray() && !Objects.equals(refSystem.getRefSystemId(), RefSystemIdEnum.TIC.getRefSystemId())) {
                        Iterator<JsonNode> it = materialDetailList.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
//                            objectItem.put(SAMPLE_NO, "");
                            objectItem.put(ID_CHECKBOX, "");
                            objectItem.put(LIST_ROW_ID_HIDE, UUID.randomUUID().toString());
                            objectItem.put(TRF_NO_HIDE, trfNo);

                            JsonNode imageList = objectItem.get(IMG_URL_LIST);
                            if (imageList != null && imageList.isArray()) {
                                Iterator<JsonNode> imgItem = imageList.iterator();
                                List<ObjectNode> imgNodes = Lists.newArrayList();
                                while (imgItem.hasNext()) {
                                    JsonNode childNodeImg = imgItem.next();
                                    ObjectNode objectImg = objectMapper.createObjectNode();
                                    objectImg.put(ID_CHECKBOX, "");
                                    objectImg.put(SHOW_PIC_HIDE, GOODS_PICTURE);
                                    objectImg.put(IMG_URL, childNodeImg.asText());
                                    // 生成 PICTURE_ID
                                    objectImg.put(LIST_ROW_ID_HIDE, UUID.randomUUID().toString());
                                    Map<String, FileInfo> fileInfoMap = fileClient.uploadTrfImageUrl(trfNo, childNodeImg.asText());
                                    if (fileInfoMap != null && fileInfoMap.containsKey(trfNo)) {
                                        // 设置fileId
                                        objectImg.put(LIST_ROW_ID_HIDE, fileInfoMap.get(trfNo).getId());
                                    }
                                    imgNodes.add(objectImg);
                                }
                                // 支持Ilayer 数据mapping 结构
                                if (CollectionUtils.isEmpty(imgNodes)) {
                                    ObjectNode objectImg = objectMapper.createObjectNode();
                                    objectImg.put(LIST_ROW_ID_HIDE, UUID.randomUUID().toString());
                                    objectImg.put(ID_CHECKBOX, "");
                                    objectImg.put(SHOW_PIC_HIDE, GOODS_PICTURE);
                                    objectImg.put(IMG_URL, "");
                                    imgNodes.add(objectImg);
                                }

                                objectItem.set(IMG_URL_LIST, objectMapper.readTree(imgNodes.toString()));
                            }

                            // Handle materialPic with flowerPicUrl using Java 8 functional programming
                            processMaterialPicFlowerUrls(objectItem, objectMapper);

                            objectNodes.add(objectItem);
                        }
                        objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                    }

                    // SGSMart trfAttachments逻辑
                    JsonNode trfAttachments = objectNode.get(TRF_ATTACHMENTS);
                    if (trfAttachments != null && trfAttachments.isArray()) {
                        Iterator<JsonNode> it = trfAttachments.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            String cloudId = StringUtils.EMPTY;
                            JsonNode cloudIdNode = objectItem.get(CLOUD_ID);
                            if (cloudIdNode != null) {
                                String textValue = cloudIdNode.asText();
                                if (!Objects.isNull(textValue)) {
                                    cloudId = textValue;
                                }
                            }
                            String attachmentUrl = fileClient.downloadByCloudID(cloudId);
                            objectItem.put(DOWNLOAD_FILE_HIDE, ATTACHMENT_URL_HIDE);
                            objectItem.put(ATTACHMENT_URL, attachmentUrl);
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(TRF_ATTACHMENTS, objectMapper.readTree(objectNodes.toString()));
                    }
                    JsonNode itemDts = objectNode.get(ITEM_DTS);
                    if (itemDts != null && itemDts.isArray()) {
                        Iterator<JsonNode> it = itemDts.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            JsonNode itemName = objectItem.get(ITEM_NAME);
                            if (itemName == null) {
                                objectItem.put(ITEM_NAME, "");
                            }
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(ITEM_DTS, objectMapper.readTree(objectNodes.toString()));
                    }

                    // TIC attachments
                    JsonNode attchment = objectNode.get(ATTACHMENTS);
                    if (attchment != null && attchment.isArray()) {
                        Iterator<JsonNode> it = attchment.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            String fileId = StringUtils.EMPTY;
                            JsonNode cloudIdNode = objectItem.get(FILE_ID);
                            if (cloudIdNode != null) {
                                String textValue = cloudIdNode.asText();
                                if (!Objects.isNull(textValue)) {
                                    fileId = textValue;
                                }
                            }
                            String attachmentUrl = fileClient.getFramewordFileByFileId(fileId);
                            objectItem.put(DOWNLOAD_FILE_HIDE, ATTACHMENT_URL_HIDE);
                            objectItem.put(ATTACHMENT_URL, attachmentUrl);
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(ATTACHMENTS, objectMapper.readTree(objectNodes.toString()));
                    }

                    break;
                case ANTA:
                    JsonNode antaOrderInfo = objectNode.get(refSystem.getSampleNode());
                    if (antaOrderInfo != null && antaOrderInfo.isArray()) {
                        Iterator<JsonNode> it = antaOrderInfo.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            objectItem.put(ORDER_NO, "");
                            objectItem.put(PACKAGE_BARCODE, trfInfo.getObjectNo());
                            objectItem.put(CREATED_DATE, DateUtils.formatDate(trfInfo.getCreatedDate()));
                            objectItem.put(CREATED_BY, trfInfo.getCreatedBy());
                            objectItem.put(GROUP_ID_HIDE, 0);
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectItem.put(DFF_FORM_ID, dffFormId);
                            objectItem.put(GRID_FORM_ID, gridFormId);
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                    }

                    if (materialDetailList != null && materialDetailList.isArray()) {
                        Iterator<JsonNode> it = materialDetailList.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
//                            objectItem.put(SAMPLE_NO, "");
                            objectItem.put(ID_CHECKBOX, "");
                            objectItem.put(LIST_ROW_ID_HIDE, UUID.randomUUID().toString());
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectItem.put(CREATED_DATE, DateUtils.formatDate(trfInfo.getCreatedDate()));
                            objectItem.put(CREATED_BY, trfInfo.getCreatedBy());
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                    }

                    JsonNode antaTestPro = objectNode.get(ANTA_TEST_PRO);
                    if (antaTestPro != null && antaTestPro.isArray()) {
                        Iterator<JsonNode> it = antaTestPro.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            objectItem.put(TRF_NO_HIDE, trfNo);
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(ANTA_TEST_PRO, objectMapper.readTree(objectNodes.toString()));
                    }
                    break;
            }

            trfInfo.setContent(objectNode.toString());

        } catch (Exception e) {
            logger.error("trfNo:{},Content 转换失败! ", trfNo);
            return;
        }
    }

    private void appendGoodsPictureList(List<ObjectNode> pictureObjectNodes,
                                        ObjectNode customerTrfContent,
                                        ObjectMapper objectMapper) throws InterruptedException {
        try {
            Map<String, String> imgMap = pictureObjectNodes.stream()
                    .collect(Collectors.toMap(
                            this::pictureId,
                            node -> node.get(IMG_URL).asText())
                    );
            StopWatch timer = new StopWatch();
            timer.start("uploading goods picture");
            Map<String, CustomResult<FileInfo>> uploadResult = fileClient.parallelUpload(SgsSystem.SODA, imgMap);
            timer.stop();

            logUploadResult(uploadResult, timer);

            pictureObjectNodes.stream()
                    .filter(node -> uploadResult.containsKey(pictureId(node)))
                    .filter(node -> uploadResult.get(pictureId(node)).isSuccess())
                    .forEach(node -> node.put(PICTURE_ID, uploadResult.get(pictureId(node)).getData().getId()));
            customerTrfContent.set(PICTURES, objectMapper.readTree(pictureObjectNodes.toString()));

            List<String> urls = uploadResult.values().stream()
                    .filter(CustomResult::isSuccess)
                    .map(CustomResult::getData)
                    .map(FileInfo::getCloudID)
                    .collect(Collectors.toList());
            customerTrfContent.set(PICTURE_URL_LIST, objectMapper.valueToTree(urls));
            UploadHolder.setGoodsPictureUrlList(uploadResult);
        } catch (ExecutionException | JsonProcessingException e) {
            logger.warn("got an error when parallel uploading picture", e);
            //ignore
        }
    }

    private static void logUploadResult(Map<String, CustomResult<FileInfo>> uploadResult, StopWatch timer) {
        Map<Boolean, List<CustomResult<FileInfo>>> statsOfUploadResults =
                uploadResult.values().stream().collect(Collectors.groupingBy(CustomResult::isSuccess));
        List<CustomResult<FileInfo>> successList = statsOfUploadResults.getOrDefault(true, Collections.emptyList());
        List<CustomResult<FileInfo>> failList = statsOfUploadResults.getOrDefault(false, Collections.emptyList());
        logger.info("uploading goods pictures total : {}/s, cnt : success {} fail {}",
                timer.getTotalTimeSeconds(), successList.size(), failList.size());
        failList.forEach(fail -> logger.warn("\t {} uploading failed!", fail));
    }

    private String pictureId(ObjectNode picNode) {
        return picNode.get(PICTURE_ID).asText();
    }

    /**
     * Process materialPic with flowerPicUrl using Java 8 functional programming
     * @param objectItem the material object item
     * @param objectMapper the ObjectMapper instance
     */
    private void processMaterialPicFlowerUrls(ObjectNode objectItem, ObjectMapper objectMapper) {
        Optional.ofNullable(objectItem.get(MATERIAL_PIC))
                .filter(JsonNode::isObject)
                .map(materialPic -> materialPic.get(FLOWER_PIC_URL))
                .filter(JsonNode::isArray)
                .map(flowerPicUrlList ->StreamSupport.stream(flowerPicUrlList.spliterator(), false)
                        .filter(Objects::nonNull)
                        .map(JsonNode::asText)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()))
                .filter(originalUrls -> !originalUrls.isEmpty())
                .ifPresent(originalUrls -> {
                    try {
                        List<String> newUrls = fileClient.parallelUpload(SgsSystem.SODA, originalUrls)
                                .stream()
                                .filter(CustomResult::isSuccess)
                                .map(CustomResult::getData)
                                .filter(Objects::nonNull)
                                .map(FileInfo::getCloudID)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());

                        if (!newUrls.isEmpty()) {
                            ObjectNode materialPicNode = (ObjectNode) objectItem.get(MATERIAL_PIC);
                            materialPicNode.set(FLOWER_PIC_URL, objectMapper.valueToTree(newUrls));
                            objectItem.set(MATERIAL_PIC, materialPicNode);
                        }
                    } catch (ExecutionException | InterruptedException e) {
                        logger.warn("Error uploading flower picture URLs for material: " + e.getMessage(), e);
                        // Continue processing without failing the entire operation
                    }
                });
    }

    /**
     * 解析 TrfNo
     *
     * @param detail
     * @param refSystem
     * @return
     */
    private String parseTrfNo(JSONObject detail, RefSystemIdEnum refSystem) {
        if (detail == null || refSystem == null || StringUtils.isBlank(refSystem.getTrfKey())) {
            if (refSystem != RefSystemIdEnum.YiLi) {
                return StringUtils.EMPTY;
            }
        }

        // 取出 trfNo
        String trfKey = refSystem.getTrfKey();
        switch (refSystem) {
            case Shein:
            case SheinSupplier:
            case LINING:
            case Camel:
            case PeaceBird:
            case SEMIR:
            case FastFish:
            case TARGET_INSPECTORIO:
            case TIC:
                if (detail.containsKey(trfKey)) {
                    return String.valueOf(detail.get(trfKey));
                }
                break;
            case F21:
            case JO_ANN:
            case Target:
            case BigLots:
            case Veyer:
            case Walmart:
            case Walmart_Group:
            case DollarTree:
            case SGSMart:
                if (detail.containsKey(HEADERS)) {
                    Object headers = detail.get(HEADERS);
                    if (headers != null) {
                        String toJSONString = JSONObject.toJSONString(headers);
                        JSONObject jsonObject = JSONObject.parseObject(toJSONString);
                        return String.valueOf(jsonObject.get(trfKey));
                    }
                }
                break;
            case ANTA: {
                Object jsonNode = detail.get(refSystem.getSampleNode());
                if (jsonNode == null) {
                    return StringUtils.EMPTY;
                }
                List<JSONObject> jsonNodes = JSON.parseArray(jsonNode.toString(), JSONObject.class);
                if (jsonNodes == null || jsonNodes.isEmpty()) {
                    return StringUtils.EMPTY;
                }
                Object trfNo = jsonNodes.get(0).get(trfKey);
                if (trfNo == null) {
                    return StringUtils.EMPTY;
                }
                return trfNo.toString();
            }
            case YiLi: {
                return StringUtils.defaultString(detail.getString("WTDH"));
            }
            case LOWES: {
                if (detail.containsKey(trfKey)) {
                    return String.valueOf(detail.get(trfKey));
                }
                break;
            }
            case Septwolves: {
                if (detail.containsKey(trfKey)) {
                    return String.valueOf(detail.get(trfKey));
                }
                break;
            }
        }
        return StringUtils.EMPTY;
    }


    /**
     * 保存Trf基本信息、撤回Trf状态
     * 提供给 iLayer调用
     * iLayer调用customerBiz接口，传递trf基本信息或者状态到customerBiz
     * TODO 修改协议需要通知iLayer
     *
     * @param reqObject
     * @return
     */
    public CustomResult sendTrfMainInfo(SendTrfMainInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();

        if (reqObject == null || reqObject.getData() == null) {
            return rspResult.fail("请检查参数data");
        }
        Integer actionType = reqObject.getActionType();
        SendTrfMainInfoType type = SendTrfMainInfoType.getType(actionType);
        if (type == null) {
            return rspResult.fail("请检查参数actionType");
        }
        String userName = reqObject.getUserName();
        TrfMainInfoReq trfMainInfoReq = reqObject.getData();
        if (StringUtils.isBlank(trfMainInfoReq.getTrfNo()) ||
                NumberUtil.toInt(trfMainInfoReq.getRefSystemId()) == 0 ||
                StringUtils.isBlank(trfMainInfoReq.getLabCode())) {
            return rspResult.fail("请检查参数trfNo, refSystemId, labCode");
        }
        String productLineCode = reqObject.getProductLineCode();
        String labCode = trfMainInfoReq.getLabCode();
        // SCI-201
        LabInfo labInfo = frameWorkClient.getLabByLabCode(labCode);
        if (Func.isEmpty(labInfo)) {
            return rspResult.fail(StrUtil.format("LabCode did not belong to {}", productLineCode));
        }
        Integer refSystemId = trfMainInfoReq.getRefSystemId();
        // TODO SCI-900
        if (Objects.equals(refSystemId, 1)) {
            refSystemId = 2;
        }
        String trfNo = trfMainInfoReq.getTrfNo();
        logger.info("iLayer调用customerBiz接口,sendTrfMainInfo,trfNo:{}, 参数：{}", trfNo, JSONObject.toJSONString(reqObject));

        TrfTodoInfoPO trfTodoInfoPO = new TrfTodoInfoPO();
        trfTodoInfoPO.setTrfNo(trfNo);
        trfTodoInfoPO.setProductLineCode(productLineCode);
        trfTodoInfoPO.setRefSystemId(refSystemId);
        trfTodoInfoPO.setLabCode(labCode);
        trfTodoInfoPO.setContent(null);
        trfTodoInfoPO.setCreatedBy(userName);
        trfTodoInfoPO.setCreatedDate(DateUtils.getNow());
        trfTodoInfoPO.setModifiedBy(userName);
        trfTodoInfoPO.setModifiedDate(DateUtils.getNow());

        rspResult.setSuccess(true);
        // 1.获取trf 是否在列表中
        TrfTodoInfoPO trfTodoInfo = trfTodoInfoExtMapper.getTrfTodoInfoList(refSystemId, trfNo);

        CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
        trf.setTrfNo(trfNo);
        trf.setRefSystemId(refSystemId);
//        trf.setTrfStatus(type.getTrfStatusEnum().getStatus());
        // 0: inactive, 1: active
        trf.setActiveIndicator(0);
        trf.setModifiedBy(userName);
        trf.setModifiedDate(DateUtils.getNow());

        if (trfTodoInfo == null && TrfStatusEnum.check(type.getTrfStatusEnum().getStatus(), TrfStatusEnum.ToBeBound)) {
//            // 数据库中没有 并且状态为 1 ，直接新增
//            if (!TrfStatusEnum.check(type.getTrfStatusEnum().getStatus(), TrfStatusEnum.ToBeBound)) {
//                return rspResult;
//            }
            trfTodoInfoPO.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
            trfTodoInfoExtMapper.batchInsert(Lists.newArrayList(trfTodoInfoPO));
            // Customer中的记录置为无效
            trfInfoExtMapper.updateCustomerTrf(trf);
            return rspResult;
        }

        //2.撤回/cancel trf情况
        if (TrfStatusEnum.check(type.getTrfStatusEnum().getStatus(), TrfStatusEnum.Canceled)) {
            // 删除记录
            trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystemId, Lists.newArrayList(trfNo));
            // Customer中的记录置为无效
            trfInfoExtMapper.updateCustomerTrf(trf);

            // 如果已经导入了Trf，则需要处理tb_trf表及相关表数据
            TrfInfoExample trfInfoExample = new TrfInfoExample();
            trfInfoExample.createCriteria().andTrfNoEqualTo(trfNo).andRefSystemIdEqualTo(refSystemId);
            List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(trfInfoExample);
            if (CollUtil.isNotEmpty(trfInfoPOList)) {
                TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOList, 0);

                TrfLogPO logPO = new TrfLogPO();
                logPO.setId(IdUtil.snowflakeId());
                logPO.setRequestId(reqObject.getRequestId());
                logPO.setRefSystemId(trfInfoPO.getRefSystemId());
                logPO.setTrfNo(trfInfoPO.getTrfNo());
                logPO.setFromStatus(trfInfoPO.getStatus());
                logPO.setToStatus(Objects.equals(type.getType(), SendTrfMainInfoType.TestRequestFormCancel.getType()) ? TrfStatusEnum.Canceled.getStatus() : trfInfoPO.getStatus());
                logPO.setChangeType(Objects.equals(type.getType(), SendTrfMainInfoType.TestRequestFormCancel.getType()) ? ChangeTypeEnum.CANCEL.getCode() : ChangeTypeEnum.REMOVE.getCode());
                logPO.setPendingFlag(trfInfoPO.getPendingFlag());
                logPO.setCreatedBy(USER_DEFAULT);
                logPO.setCreatedDate(DateUtil.now());
                logPO.setModifiedBy(USER_DEFAULT);
                logPO.setModifiedDate(DateUtil.now());
                trfLogMapper.insert(logPO);

                if (Objects.equals(type.getType(), SendTrfMainInfoType.TestRequestFormCancel.getType())) {
                    trfInfoPO.setStatus(TrfStatusEnum.Canceled.getStatus());
                } else {
                    trfInfoPO.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());
                }
                trfInfoPO.setModifiedBy(USER_DEFAULT);
                trfInfoPO.setModifiedDate(DateUtils.now());
                trfInfoMapper.updateByPrimaryKey(trfInfoPO);
            }

            return rspResult;
        }

        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     * 模糊搜索TrfNo
     *
     * @param reqObject
     * @return
     */
    public CustomResult<List<TrfTodoDTO>> searchTrfNo(SearchTrfNoReq reqObject) {
        CustomResult<List<TrfTodoDTO>> rspResult = new CustomResult();

        if (reqObject == null
                || StringUtils.isBlank(reqObject.getTrfNo())
                || NumberUtil.toInt(reqObject.getRefSystemId()) == 0) {
            return rspResult.fail("请检查参数trfNo, refSystemId");
        }
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String labCode = user.getCurrentLabCode();
        String trfNo = reqObject.getTrfNo();
        Integer refSystemId = reqObject.getRefSystemId();
        if (trfNo.length() < 5) {
            rspResult.setSuccess(true);
            return rspResult;
        }

        List<TrfTodoDTO> trfTodoInfoList = trfTodoInfoExtMapper.getTrfTodoInfoListByTrfNo(refSystemId, labCode, trfNo);

        rspResult.setData(trfTodoInfoList);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult queryCustomerTrfInfoByTrfNo(SearchTrfNoReq reqObject) {
        logger.info(" queryCustomerTrfInfoByTrfNo,param:{}", JSON.toJSONString(reqObject));
        CustomResult rspResult = new CustomResult();
        if (Objects.isNull(reqObject) || NumberUtil.toInt(reqObject.getRefSystemId()) <= 0 || StringUtils.isBlank(reqObject.getTrfNo())) {
            return rspResult.fail("param error");
        }
        CustomerTrfInfoRsp infoRsp = trfInfoExtMapper.getTrfInfo(reqObject.getRefSystemId(), reqObject.getTrfNo());
        rspResult.setSuccess(true);
        rspResult.setData(infoRsp);
        return rspResult;
    }


    public CustomResult<Integer> checkCustomerRule(SearchTrfNoReq reqObject) {
        CustomResult<Integer> result = new CustomResult<>();
        Integer refSystemId = reqObject.getRefSystemId();
        String productLineCode = reqObject.getProductLineCode();
        String trfNo = reqObject.getTrfNo();
        if (!RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.values()) || StringUtils.isBlank(trfNo) || StringUtils.isBlank(productLineCode)) {
            return result.fail("Parameter Error");
        }

        CustomerConfigRsp customerConfig = customerConfigClient.getCustomerConfig(refSystemId, productLineCode);
        if (customerConfig == null) {
            return result.fail("Check Data Fail");
        }
        String customerRules = customerConfig.getCustomerRules();
        if (StringUtils.isBlank(customerRules)) {
            result.setSuccess(true);
            return result;
        }

        CheckCustomerRuleRsp rsp = null;
        try {
            rsp = JSONObject.parseObject(customerRules, CheckCustomerRuleRsp.class);
        } catch (Exception e) {
            logger.info("查询配置信息解析CustomerRule异常：", e);
        }

        if (rsp == null) {
            return result.fail("Check data rules fail.");
        }

        List<TrfContentDTO> trfs = trfInfoExtMapper.getCustTrfContentInfo(Lists.newArrayList(trfNo), null);
        if (CollectionUtils.isEmpty(trfs)) {
            return result.fail("TRF info has changed,please reload");
        }
        //只有compoonentRule用到
        Integer createType = trfs.get(0).getCreateType();

        /**
         * 解析规则为 先解析ruleList，如果不存在rule 再解析外层的rule
         */

        //配置规则针对希音，希音供应商
        //一下两个配置对象，只能其中一个有值，不能同时存在，同时存在，优先解析createOrderSelectComponentRule
        CreateOrderSampleRuleItem createOrderSelectComponentRule = rsp.getCreateOrderSelectComponentRule();
        CreateOrderSampleRuleItem createOrderSelectSampleRule = rsp.getCreateOrderSelectSampleRule();

        if (createOrderSelectComponentRule != null) {

            Integer defaultRule = createOrderSelectComponentRule.getRule();
            List<CustomerRuleItem> ruleList = createOrderSelectComponentRule.getRuleList();
            if (CollectionUtils.isEmpty(ruleList)) {
                result.setSuccess(true);
                result.setData(defaultRule);
                return result;
            }

            //没有对应createType的值，返回默认rule
            CustomerRuleItem customerRuleItem = ruleList.stream().filter(r -> NumberUtil.equals(createType, r.getCreateType())).findFirst().orElse(null);

            if (customerRuleItem == null) {
                result.setSuccess(true);
                result.setData(defaultRule);
                return result;
            }

            Integer createRule = customerRuleItem.getRule();
            result.setSuccess(true);
            result.setData(createRule);
            return result;

        }

        if (createOrderSelectSampleRule != null) {
            Integer defaultRule = createOrderSelectSampleRule.getRule();
            result.setSuccess(true);
            result.setData(defaultRule);
            return result;
        }

        result.setSuccess(true);
        return result;
    }

    public CustomResult<GetTrfDetailPrintRsp> getTrfDetailPrintInfo(GetTrfDetailPrintReq reqObject) {
        logger.info("GetTrfDetailPrintInfo request: {}", JSON.toJSONString(reqObject, SerializerFeature.WriteMapNullValue));

        CustomResult<GetTrfDetailPrintRsp> rspResult = new CustomResult<>();

        UserInfo userInfo = UserHelper.getLocalUser();
        if (userInfo == null) {
            rspResult.fail("Invalid session, please login again before processing");
            return rspResult;
        }

        List<TrfDetailReq> trfDetailReqList = new ArrayList<>();
        reqObject.getPrintItemList().forEach(printItem -> {
            List<CustomerTrfInfoRsp> customerTrfInfoRspList =
                    mergeTrfNoListAndPackageBarcodeList(printItem.getTrfNoList(), null);
            if (!CollectionUtils.isEmpty(customerTrfInfoRspList)) {
                for (CustomerTrfInfoRsp customerTrfInfoRsp : customerTrfInfoRspList) {
                    TrfDetailReq trfDetailReq = new TrfDetailReq();
                    trfDetailReq.setRefSystemId(printItem.getRefSystemId());
                    trfDetailReq.setCustomerGroupCode(printItem.getCustomerGroupCode());
                    trfDetailReq.setTrfNo(Collections.singletonList(customerTrfInfoRsp.getTrfNo()));
                    trfDetailReq.setSceneTypes(printItem.getSceneTypeList());
                    trfDetailReq.setToken(reqObject.getToken());
                    trfDetailReq.setSign(reqObject.getSign());
                    trfDetailReq.setRequestId(reqObject.getRequestId());

                    String productLineCode = printItem.getProductLineCode();
                    trfDetailReq.setProductLineCode(StringUtils.isBlank(productLineCode) ? reqObject.getProductLineCode() : productLineCode);

                    trfDetailReqList.add(trfDetailReq);
                }
            }
        });

        //TODO max limit 配置化
        if (trfDetailReqList.size() > 10) {
            logger.warn("The number of TRFs to be printed was {}", trfDetailReqList.size());
            return rspResult.fail("The number of TRFs to be printed exceeded the maximum limit of 10");
        }

        RenderTrfDetailToFileReq renderTrfDetailToFileReq = new RenderTrfDetailToFileReq();
        trfDetailReqList.forEach(trfDetailReq -> {
            CustomResult customResult = getCustomizedTrfDetailData(trfDetailReq);
            if (customResult.isSuccess() && customResult.getData() != null) {
                JSONObject jsonObject = JSON.parseObject(customResult.getData().toString(), Feature.OrderedField);

                RenderTrfDetailToFileReq.RenderTrfDetailToFileItemPOJO renderTrfDetailToFileItem =
                        new RenderTrfDetailToFileReq.RenderTrfDetailToFileItemPOJO();

                RenderTrfDetailToFileReq.VariableDataListPOJO variableDataList =
                        new RenderTrfDetailToFileReq.VariableDataListPOJO();
                RenderTrfDetailToFileReq.HeaderPOJO header = new RenderTrfDetailToFileReq.HeaderPOJO();
                header.setCustomerName(RefSystemIdEnum.getName(trfDetailReq.getRefSystemId()));
                header.setTrfNo(trfDetailReq.getTrfNo().get(0));
                variableDataList.setHeader(header);

                RenderTrfDetailToFileReq.FooterPOJO footer = new RenderTrfDetailToFileReq.FooterPOJO();
                footer.setPrintBy(UserHelper.getLocalUser().getName());
                footer.setPrintAt(DateUtils.now());
                variableDataList.setFooter(footer);

                JSONArray attachmentArray = jsonObject.getJSONArray("trf_detail_attachment");
                variableDataList.setAttachmentList(filterSpecKeys(attachmentArray));

                JSONArray checkitemArray = jsonObject.getJSONArray("trf_detail_checkitem");
                variableDataList.setCheckItemList(filterSpecKeys(checkitemArray));

                JSONArray pictureArray = jsonObject.getJSONArray("trf_detail_picture");
                variableDataList.setPictureList(filterSpecKeys(pictureArray));

                JSONArray productArray = jsonObject.getJSONArray("trf_detail_product");
                variableDataList.setProductList(filterSpecKeys(productArray));

                JSONArray general = jsonObject.getJSONArray("trf_detail_general");
                if (general != null && !general.isEmpty()) {
                    variableDataList.setGeneral(genDffFieldList(general));
                }

                JSONArray customerArray = jsonObject.getJSONArray("trf_detail_customer");
                if (customerArray != null && !customerArray.isEmpty()) {
                    variableDataList.setCustomerList(genDffFieldList(customerArray));
                }

                RenderTrfDetailToFileReq.DatasourcePOJO datasource =
                        new RenderTrfDetailToFileReq.DatasourcePOJO();
                datasource.setVariableDataList(variableDataList);
                datasource.setProductLineCode(trfDetailReq.getProductLineCode());

                RenderTrfDetailToFileReq.InfoPOJO info = new RenderTrfDetailToFileReq.InfoPOJO();
                info.setBuCode(trfDetailReq.getProductLineCode());
                info.setGroupCode(trfDetailReq.getCustomerGroupCode());
                info.setTrfNo(trfDetailReq.getTrfNo().get(0));

                renderTrfDetailToFileItem.setInfo(info);
                renderTrfDetailToFileItem.setDatasource(datasource);

                renderTrfDetailToFileReq.getItemList().add(renderTrfDetailToFileItem);
            }
        });

        DigitalReportRenderToFileRsp digitalReportRenderToFileRsp = digitalReportClient.renderTrfDetailToFile(renderTrfDetailToFileReq);
        if (Objects.equals(digitalReportRenderToFileRsp.getResult(), 0)) {
            rspResult.setSuccess(true);
            String accessUrl = fileClient.generateUrlByCloudID(digitalReportRenderToFileRsp.getCloudId(), FileNetworkType.internet.getNetworkType(), "4320");
            rspResult.setData(new GetTrfDetailPrintRsp(accessUrl, digitalReportRenderToFileRsp.getUrl()));
        } else {
            rspResult.fail(digitalReportRenderToFileRsp.getErrMsg());
            logger.error("Failed to get Trf Detail render file info. ErrorMsg: {}", digitalReportRenderToFileRsp.getErrMsg());
        }

        logger.info("GetTrfDetailPrintInfo response: {}", JSON.toJSONString(rspResult, SerializerFeature.WriteMapNullValue));
        return rspResult;
    }

    private List<RenderTrfDetailToFileReq.DffPOJO> genDffFieldList(JSONArray jsonArray) {
        List<RenderTrfDetailToFileReq.DffPOJO> dffFieldList = new ArrayList<>();
        jsonArray.forEach(jObj -> {
            JSONObject geneObj = (JSONObject) jObj;
            for (Map.Entry<String, Object> entry : geneObj.entrySet()) {
                if (!entry.getKey().endsWith("_hide")) {
                    RenderTrfDetailToFileReq.DffPOJO dffField = new RenderTrfDetailToFileReq.DffPOJO();
                    dffField.setLabel(entry.getKey());
                    dffField.setValue(entry.getValue());
                    dffFieldList.add(dffField);
                }
            }
        });
        return dffFieldList;
    }

    private static final List<String> SPEC_KEY_LIST =
            Arrays.asList("id_checkbox");

    private JSONArray filterSpecKeys(JSONArray jsonArray) {
        JSONArray newArray = new JSONArray();
        if (jsonArray != null && !jsonArray.isEmpty()) {
            jsonArray.forEach(jsonObject -> {
                JSONObject newObj = new JSONObject(true);
                JSONObject jObj = (JSONObject) jsonObject;
                Set<String> keys = jObj.keySet();
                for (String key : keys) {
                    if (!key.endsWith("_hide") && !SPEC_KEY_LIST.contains(key)) {
                        newObj.put(key, jObj.get(key));
                    }
                }
                newArray.add(newObj);
            });
        }
        return newArray;
    }

    private List<CustomerTrfInfoRsp> mergeTrfNoListAndPackageBarcodeList(List<String> trfNoList,
                                                                         List<String> packageBarcodeList) {
        List<CustomerTrfInfoRsp> trfInfoRspList = trfInfoExtMapper.getTrfInfoList(trfNoList, true, packageBarcodeList);
        if (CollectionUtils.isEmpty(trfInfoRspList)) {
            logger.error("获取Trf信息失败. TrfNoList={}， PackageBarcodeList={}",
                    JSONArray.toJSONString(trfNoList, SerializerFeature.WriteMapNullValue),
                    JSONArray.toJSONString(packageBarcodeList, SerializerFeature.WriteMapNullValue));
            throw new BizException("获取Trf 信息失败，请检查数据");
        }

        List<CustomerTrfInfoRsp> mergedCustomerTrfInfoRspList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(packageBarcodeList)) {
            for (String packageBarcode : packageBarcodeList) {
                CustomerTrfInfoRsp trfInfoRsp = trfInfoRspList.stream().filter(info -> info.getObjectNo().equalsIgnoreCase(packageBarcode)).findFirst().orElse(null);
                if (trfInfoRsp != null) {
                    mergedCustomerTrfInfoRspList.add(trfInfoRsp);
                } else {
                    logger.error("获取Trf信息失败. PackageBarcode={}", packageBarcode);
                    throw new BizException(String.format("获取 %s 信息失败，请检查数据是否import", packageBarcode));
                }
            }
        }

        if (!CollectionUtils.isEmpty(trfNoList)) {
            for (String trfNo : trfNoList) {
                CustomerTrfInfoRsp trfInfoRsp = trfInfoRspList.stream().filter(info -> info.getTrfNo().equalsIgnoreCase(trfNo)).findFirst().orElse(null);
                if (trfInfoRsp != null) {
                    long count = mergedCustomerTrfInfoRspList.stream().filter(customerTrfInfoRsp -> customerTrfInfoRsp.getTrfNo().equalsIgnoreCase(trfNo)).count();
                    if (count == 0) {
                        mergedCustomerTrfInfoRspList.add(trfInfoRsp);
                    }
                } else {
                    logger.error("获取Trf信息失败. TrfNo={}", trfNo);
                    throw new BizException(String.format("获取 %s 信息失败，请检查数据是否import", trfNo));
                }
            }
        }

        return mergedCustomerTrfInfoRspList.size() == 0 ? Collections.emptyList() : mergedCustomerTrfInfoRspList;
    }

    //    获取客制化Trf详情数据
    private CustomResult<Object> getCustomizedTrfDetailData(TrfDetailReq reqObject) {
        logger.info("Get customized trf detail data, request: {}", JSON.toJSONString(reqObject, SerializerFeature.WriteMapNullValue));

        CustomResult<Object> rspResult = new CustomResult<>();

        if (reqObject == null ||
                (CollectionUtils.isEmpty(reqObject.getTrfNo()) && CollectionUtils.isEmpty(reqObject.getPackageBarcode())) ||
                CollectionUtils.isEmpty(reqObject.getSceneTypes())) {
            return rspResult.fail("请检查 trfNo, packageBarcode, sceneTypes!");
        }

        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(reqObject.getRefSystemId());
        if (refSystem == null) {
            return rspResult.fail("请检查 Ref SystemId");
        }

        List<String> trfNoList = reqObject.getTrfNo();
        List<String> packageBarcodeList = reqObject.getPackageBarcode();
        List<String> sceneTypeList = reqObject.getSceneTypes();

        List<CustomerTrfInfoRsp> customerTrfInfoRspList = mergeTrfNoListAndPackageBarcodeList(trfNoList, packageBarcodeList);
        List<String> uniqueTrfNoList = customerTrfInfoRspList.stream().map(CustomerTrfInfoRsp::getTrfNo).collect(Collectors.toList());

        // 希音 trf 需要校验是否同时有 自建单 和非自建单，有的情况，禁止查看detail
        switch (refSystem) {
            case Shein:
            case SheinSupplier:
                List<TrfContentDTO> trfCreateType = trfInfoExtMapper.getTrfContentInfo(uniqueTrfNoList, null);
                if (CollectionUtils.isEmpty(trfCreateType)) {
                    return rspResult.fail("Shein订单：获取Trf 信息(CreateType)失败，请检查数据");
                }
                Set<Integer> createTypeSets = trfCreateType.stream().map(TrfContentDTO::getCreateType).collect(Collectors.toSet());
                if (createTypeSets.contains(CreateType.CreateTypeSelf.getType()) && createTypeSets.contains(CreateType.CreateTypeOther.getType())) {
                    return rspResult.fail("Shein订单:不能同时选择自建和非自建TrfNo，请重新选择！");
                }
                break;
        }

        // 手动处理SampleNo
        int preSampleNo = 1;
        Map<String, Integer> trfSampleMaps = Maps.newHashMap();
        for (CustomerTrfInfoRsp trfInfo : customerTrfInfoRspList) {
            if (trfSampleMaps.containsKey(trfInfo.getTrfNo())) {
                continue;
            }
            trfSampleMaps.put(trfInfo.getTrfNo(), preSampleNo);
            preSampleNo++;
        }

        List<Object> trfDataList = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        for (CustomerTrfInfoRsp trf : customerTrfInfoRspList) {
            if (StringUtils.isBlank(trf.getContent())) {
                continue;
            }
            try {
                JsonNode jsonNode = objectMapper.readTree(trf.getContent());
                ObjectNode objectNode = jsonNode.deepCopy();

                // Process materialDetailList to convert flowerPicUrl cloudIds to downloadable URLs
                Optional.ofNullable(objectNode.get("materialDetailList"))
                        .filter(JsonNode::isArray)
                        .map(materialDetailListNode -> StreamSupport.stream(materialDetailListNode.spliterator(), false))
                        .ifPresent(materialDetailStream ->
                            materialDetailStream
                                .filter(JsonNode::isObject)
                                .map(materialDetailNode -> (ObjectNode) materialDetailNode)
                                .forEach(materialDetailObjectNode ->
                                    Optional.ofNullable(materialDetailObjectNode.get(MATERIAL_PIC))
                                            .filter(JsonNode::isObject)
                                            .map(materialPicNode -> (ObjectNode) materialPicNode)
                                            .ifPresent(materialPicObjectNode ->
                                                Optional.ofNullable(materialPicObjectNode.get(FLOWER_PIC_URL))
                                                        .filter(JsonNode::isArray)
                                                        .ifPresent(flowerPicUrlNode -> {
                                                            ArrayNode newFlowerPicUrlArray = StreamSupport.stream(flowerPicUrlNode.spliterator(), false)
                                                                    .filter(Objects::nonNull)
                                                                    .map(JsonNode::asText)
                                                                    .filter(StringUtils::isNotBlank)
                                                                    .map(this::convertCloudIdToDownloadUrl)
                                                                    .collect(objectMapper::createArrayNode,
                                                                            ArrayNode::add,
                                                                            (array1, array2) -> array1.addAll(array2));
                                                            materialPicObjectNode.set(FLOWER_PIC_URL, newFlowerPicUrlArray);
                                                        })
                                            )
                                )
                        );

                if (Objects.equals(refSystem.getRefSystemId(), RefSystemIdEnum.Shein.getRefSystemId())) {
                    JsonNode goodsPictureUrlListNode = objectNode.get(PICTURES);
                    if (Func.isEmpty(goodsPictureUrlListNode)) {
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        JsonNode node = objectNode.get(PICTURE_ID);
                        ObjectNode objectItem = objectMapper.createObjectNode();
                        objectItem.put(ID_CHECKBOX, "");
                        objectItem.put(TRF_NO, trf.getTrfNo());
                        objectItem.put(IMG_URL, "");
                        objectItem.put(PICTURE_ID, UUID.randomUUID().toString());
                        if (Func.isNotEmpty(node)) {
                            String textValue = node.asText();
                            JsonNode jsonNode1 = objectNode.get(GOODS_PICTURE_URL);
                            objectItem.put(IMG_URL, jsonNode1.asText(""));
                            objectItem.put(PICTURE_ID, textValue);
                            objectNodes.add(objectItem);
                        }
                        objectNode.put(PICTURES, objectMapper.readTree(objectNodes.toString()));
                    }
                    if (Func.isEmpty(objectNode.get(GOODS_COLOR))) {
                        objectNode.put(GOODS_COLOR, "");
                    } else {
                        objectNode.put(GOODS_COLOR, objectNode.get(GOODS_COLOR));
                    }
                }

                switch (refSystem) {
                    case Shein:
                    case LINING:
                    case Camel:
                    case PeaceBird:
                    case SEMIR:
                    case FastFish:
                    case TIC:
                        objectNode.put(SAMPLE_HIDE, NumberUtil.numberToLetter(trfSampleMaps.get(trf.getTrfNo())));
                        objectNode.put(TRF_REPORT_LEVEL_HIDE, trf.getTrfReportLevel());
                        JsonNode sampleForm = objectNode.get(SAMPLE_FORM);
                        if (sampleForm != null && sampleForm.isArray()) {
                            Iterator<JsonNode> it = sampleForm.iterator();
                            List<ObjectNode> objectNodes = Lists.newArrayList();
                            while (it.hasNext()) {
                                JsonNode childNode = it.next();
                                ObjectNode objectItem = childNode.deepCopy();
                                if ((objectItem.get(LIST_ROW_ID_HIDE)== null||objectItem.get(LIST_ROW_ID_HIDE).isEmpty()) && RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.TIC)) {
                                    objectItem.put(LIST_ROW_ID_HIDE, objectItem.get(SAMPLE_NO).asText());
                                }
                                objectNodes.add(objectItem);
                            }
                            objectNode.set(SAMPLE_FORM, objectMapper.readTree(objectNodes.toString()));
                        }
                        if (Objects.equals(trf.getTrfReportLevel(), TrfReportLevelEnum.SAMPLE_ONE.getCode())) {
                            String trfNo = trf.getTrfNo();
                            Integer refSystemId = trf.getRefSystemId();
                            // 查询trfReportLevel表数据
                            List<TrfReportLevelPO> trfReportLevelPOS = trfDomainService.queryInfoByTrfNo(trfNo, refSystemId);
                            if (Func.isEmpty(trfReportLevelPOS)) {
                                trfReportLevelPOS = new ArrayList<>();
                            }
//                            if (Func.isNotEmpty(trfReportLevelPOS)) {
                            List<String> sampleNoList = trfReportLevelPOS.stream().map(TrfReportLevelPO::getBizNo).collect(Collectors.toList());
                            JsonNode sampleForm2 = objectNode.get(SAMPLE_FORM);
                            if (sampleForm2 != null && sampleForm2.isArray()) {
                                Iterator<JsonNode> it = sampleForm2.iterator();
                                List<ObjectNode> objectNodes = Lists.newArrayList();
                                while (it.hasNext()) {
                                    JsonNode childNode = it.next();
                                    ObjectNode objectItem = childNode.deepCopy();
                                    if (sampleNoList.contains(objectItem.get(SAMPLE_NO).asText())) {
                                        objectItem.put(CHECKBOX_HIDE, 1);
                                    } else {
                                        objectItem.put(CHECKBOX_HIDE, 0);
                                    }
                                    objectNodes.add(objectItem);
                                }
                                objectNode.set(SAMPLE_FORM, objectMapper.readTree(objectNodes.toString()));
                            }
//                            }
                        }
                        this.resetObjectNode(trf, objectMapper, objectNode, refSystem, trfSampleMaps.get(trf.getTrfNo()));
                        break;
                    case SheinSupplier:
                    case ANTA:
                        this.resetObjectNode(trf, objectMapper, objectNode, refSystem, trfSampleMaps.get(trf.getTrfNo()));
                        break;
                }
            } catch (Exception ex) {
                logger.error("TrfNo:{}, Content转换失败! Error: {}", trf.getTrfNo(), ex);
                rspResult.setMsg(ex.getMessage());
                rspResult.fail("Trf Content转换失败");
                return rspResult;
            }
            trfDataList.add(JSON.parse(trf.getContent()));
        }

//         请求iLayer获取客制化数据结构
        ConvertDataReq reqData = new ConvertDataReq();
        // TODO SCI-900
        reqData.setRefSystemId(reqObject.getRefSystemId());
        reqData.setCustomerGroupCode(reqObject.getCustomerGroupCode());
        maybeRewriteRefSystemId(reqObject.getRefSystemId(), reqData);

        reqData.setProductLineId(refSystem == RefSystemIdEnum.TARGET_INSPECTORIO ? 0 : ProductLineContextHolder.getProductLineId());
        reqData.setSceneTypes(sceneTypeList);
        JsonNode jsonNode = iLayerClient.convertData(reqData, trfDataList);
        if (Func.isEmpty(jsonNode)) {
            return rspResult.fail("获取TRF信息失败，请检查数据！");
        }

        logger.info("Get customized trf detail data, response: {}", JSON.toJSONString(jsonNode.toString(), SerializerFeature.WriteMapNullValue));

        rspResult.setData(jsonNode);
        rspResult.setSuccess(true);

        return rspResult;
    }

    private void maybeRewriteRefSystemId(Integer refSystemId, ConvertDataReq reqData) {
        Boolean isIntegratedSGSMart = configClient.getCustomerConfigBy(refSystemId)
                .map(CustomerGeneralConfig::isIntegratedSGSMart)
                .orElse(false);
        if (isIntegratedSGSMart || Objects.equals(refSystemId, 2)) {
            iLayerClient.rewriteRefSystemId(reqData);
        }
    }

    /**
     * Convert cloudId to downloadable URL using FileClient
     * @param cloudId the cloud ID to convert
     * @return downloadable URL or original cloudId if conversion fails
     */
    private String convertCloudIdToDownloadUrl(String cloudId) {
        try {
            String downloadUrl = fileClient.downloadByCloudID(cloudId);
            if (StringUtils.isNotBlank(downloadUrl)) {
                return downloadUrl;
            } else {
                logger.warn("FileClient returned blank URL for cloudId: {}", cloudId);
                return cloudId; // Keep original if conversion returns blank
            }
        } catch (Exception e) {
            logger.warn("Failed to convert cloudId {} to download URL: {}", cloudId, e.getMessage());
            return cloudId; // Keep original if conversion fails
        }
    }

}
