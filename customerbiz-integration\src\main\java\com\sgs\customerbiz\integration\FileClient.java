package com.sgs.customerbiz.integration;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.customerbiz.core.config.InterfaceConfig;
import com.sgs.customerbiz.core.util.HttpClientUtil;
import com.sgs.customerbiz.core.util.NumberUtil;
import com.sgs.customerbiz.facade.model.file.FileInfo;
import com.sgs.customerbiz.facade.model.file.MultipartUploadFile;
import com.sgs.customerbiz.facade.model.file.UploadFileInfo;
import com.sgs.customerbiz.integration.exceptions.UncheckUploadException;
import com.sgs.customerbiz.integration.exceptions.UploadException;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.HttpResult;
import com.sgs.otsnotes.facade.model.dto.FileDTO;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class FileClient {

    private static final Logger logger = LoggerFactory.getLogger(FileClient.class);

    private final InterfaceConfig interfaceConfig;

    private static final Integer UPLOAD_SUCCESS = 200;

    private final ExecutorService executor;

    public FileClient(InterfaceConfig interfaceConfig) {
        this.interfaceConfig = interfaceConfig;
        this.executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    }

    public <K> Map<K, CustomResult<FileInfo>> sequenceUpload(SgsSystem system,
                                               Map<K, String> toUpload) throws UncheckUploadException {
        return toUpload.entrySet().stream()
                .map(entry ->
                        Pair.of(entry.getKey(), UploadBody.of(system, entry.getValue()))
                )
                .map(this::upload)
                .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
    }

    public <K> Map<K, CustomResult<FileInfo>> parallelUpload(SgsSystem system,
                                               Map<K, String> toUpload) throws ExecutionException, InterruptedException {
        List<CompletableFuture<Pair<K, CustomResult<FileInfo>>>> uploadResultArray = toUpload.entrySet().stream()
                .map(entry ->
                        Pair.of(entry.getKey(), UploadBody.of(system, entry.getValue()))
                )
                .map(this::completableUpload)
                .collect(Collectors.toList());
        CompletableFuture.allOf(uploadResultArray.toArray(new CompletableFuture[]{})).get();
        return uploadResultArray.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
    }

    public List<CustomResult<FileInfo>> parallelUpload(SgsSystem system, List<String> toUploadUrls)
            throws ExecutionException, InterruptedException {
        List<CompletableFuture<CustomResult<FileInfo>>> uploadResultArray = toUploadUrls.stream()
                .map(url -> UploadBody.of(system, url))
                .map(this::completableUpload)
                .collect(Collectors.toList());
        CompletableFuture.allOf(uploadResultArray.toArray(new CompletableFuture[]{})).get();
        return uploadResultArray.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    @NotNull
    private <K> CompletableFuture<CustomResult<FileInfo>> completableUpload(UploadBody uploadBody) {
        return CompletableFuture.supplyAsync(() ->
                resultUploadBy(uploadBody.getSystemId(), uploadBody.getUrl()),
                executor
        );
    }

    @NotNull
    private <K> CompletableFuture<Pair<K, CustomResult<FileInfo>>> completableUpload(Pair<K, UploadBody> pair) {
        return CompletableFuture.supplyAsync(() ->
                Pair.of(
                        pair.getLeft(),
                        resultUploadBy(pair.getRight().getSystemId(), pair.getRight().getUrl())
                ),
                executor
        );
    }

    @NotNull
    private <K> Pair<K, CustomResult<FileInfo>> upload(Pair<K, UploadBody> pair) {
        K key = pair.getLeft();
        UploadBody body = pair.getRight();
        CustomResult<FileInfo> fileInfo = resultUploadBy(body.getSystemId(), body.getUrl());
        return Pair.of(key, fileInfo);
    }

    @Builder
    @Getter
    public static class UploadBody {

        private String systemId;
        private String url;

        public static UploadBody of(SgsSystem system, String url) {
            Objects.requireNonNull(system, "system must be not null");
            Objects.requireNonNull(url, "url must be not null");
            return of(String.valueOf(system.getSgsSystemId()), url);
        }

        public static UploadBody of(String systemId, String url) {
            return UploadBody.builder().systemId(systemId).url(url).build();
        }
    }

    @Getter
    public static class Pair<L, R> {
        private final L left;
        private final R right;

        public Pair(L left, R right) {
            this.left = left;
            this.right = right;
        }

        public static <V1, V2> Pair<V1, V2> of(V1 left, V2 right) {
            return new Pair<>(left, right);
        }
    }

    public CustomResult<FileInfo> resultUploadBy(String systemId,
                                    String url) {
        try {
            return uploadOk(uploadBy(systemId, url));
        } catch (UploadException e) {
            return uploadFail(e);
        }
    }

    private static CustomResult<FileInfo> uploadOk(FileInfo data) {
        CustomResult<FileInfo> right = CustomResult.newSuccessInstance();
        right.setData(data);
        return right;
    }

    private static CustomResult<FileInfo> uploadFail(Throwable t) {
        CustomResult<FileInfo> right = new CustomResult<>(false);
        right.setMsg(t.getMessage());
        return right;
    }

    public FileInfo uploadBy(String systemId,
                             String url) throws UploadException {
        List<FileInfo> fileInfos = uploadByFrameworkAPI(interfaceConfig.getFrameWorkApiUrl(), systemId, url);
        if(fileInfos.size()!=1){
            throw new UploadException("except 1 fileInfo but found " + fileInfos.size());
        }
        return fileInfos.get(0);
    }

    public static List<FileInfo> uploadByFrameworkAPI(String frameworkAPIUrl,
                                                      String systemId,
                                                      String url)
            throws UploadException {
        String api = String.format("%s/file/uploadForUrl", frameworkAPIUrl);
        try {
            String respStr = HttpClientUtil.postJson(
                    api,
                    JSONObject.toJSONString(buildBody(systemId, url))
            );
            return JSON.parseArray(
                    JSONObject.parseObject(respStr).get("result").toString(),
                    FileInfo.class
            );
        } catch (Throwable ex) {
            throw new UploadException("upload to " + api + " got an error. [url: "+ url +"]", ex);
        }
    }

    @NotNull
    private static Map<String, Object> buildBody(String systemId, String url) {
        Map<String,Object> params= Maps.newHashMap();
        Map<String,Object> urlsValue= Maps.newHashMap();
        params.put("systemID", systemId);
        urlsValue.put("fileUrl", url);
        params.put("urls", ImmutableList.of(urlsValue));
        return params;
    }

    /**
     * 上传 Image By Url
     *
     * @param trfNo
     * @param url
     * @return
     */
    public Map<String, FileInfo> uploadTrfImageUrl(String trfNo, String url) {
        Map<String, FileInfo> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(url)) {
            return resultMap;
        }
        UploadFileInfo fileInfo = new UploadFileInfo();
//        fileInfo.setSystemId(NumberUtil.toInt(Constants.SYSTEM));
        fileInfo.setBuId(NumberUtil.toInt(SgsSystem.SODA.getSgsSystemId()));
        fileInfo.setLocationID(11);
        fileInfo.setImageUrl(url);
        CustomResult<List<FileInfo>> result = this.batchUploadFileByUrl(fileInfo);
        if (!result.isSuccess()) {
            logger.error("importTrfNO：{} 批量上传文件失败,urls:{}", trfNo, url);
            return resultMap;
        }
        List<FileInfo> fileInfos = result.getData();
        Map<String, FileInfo> fileMap = fileInfos.stream().collect(Collectors.toMap(FileInfo::getAttachmentName, Function.identity(), (o1, o2) -> o1));

        String fileName = FilenameUtils.getName(url);
        fileName = fileName.substring(0, fileName.indexOf("."));
        resultMap.put(trfNo, fileMap.get(fileName));

        return resultMap;
    }

    public FileInfo uploadFile(String url) {
        if (StringUtils.isBlank(url)) {
            return new FileInfo();
        }
        UploadFileInfo fileInfo = new UploadFileInfo();
//        fileInfo.setSystemId(NumberUtil.toInt(Constants.SYSTEM));
        fileInfo.setBuId(NumberUtil.toInt(SgsSystem.SODA.getSgsSystemId()));
        fileInfo.setLocationID(11);
        CustomResult<List<FileInfo>> result = this.batchUploadFile(fileInfo, Lists.newArrayList(url));
        if (!result.isSuccess()) {
            logger.error("uploadFile：{} 上传文件失败,url:{},message={}", url, result.getMsg());
            return  new FileInfo();
        }
        List<FileInfo> fileInfos = result.getData();

        return CollUtil.get(fileInfos, 0);
    }

    /**
     * @param uploadFileList
     * @return
     */
    public CustomResult<List<FileInfo>> uploadFile(int systemId,int buId,List<MultipartUploadFile> uploadFileList){
        CustomResult<List<FileInfo>> rspResult = new CustomResult<>();
        rspResult.setSuccess(true);
        try {
            String uploadUrl = String.format("%s/file/doUpload", interfaceConfig.getFrameWorkApiUrl());
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            multipartEntityBuilder.setMode(HttpMultipartMode.RFC6532);
            multipartEntityBuilder.setCharset(StandardCharsets.UTF_8);
            multipartEntityBuilder.setContentType(ContentType.MULTIPART_FORM_DATA);
            multipartEntityBuilder.addTextBody("systemID", String.valueOf(systemId));
            multipartEntityBuilder.addTextBody("buID", String.valueOf(buId));
            for (MultipartUploadFile file : uploadFileList) {
                multipartEntityBuilder.addBinaryBody("file", file.getInputStream(), ContentType.APPLICATION_OCTET_STREAM, file.getFilename());
            }
            org.apache.http.HttpEntity httpEntity = multipartEntityBuilder.build();
            String result = HttpClientUtil.sendPost(uploadUrl, httpEntity);
            if (StringUtils.isBlank(result)) {
                return rspResult.fail("Failed to upload file");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (!UPLOAD_SUCCESS.equals(jsonObject.getInteger("status"))){
                logger.error("Failed to upload file ,respCode:{} ,errMsg:{}",jsonObject.getString("status"),jsonObject.getString("message"));
                return rspResult.fail("Failed to upload file");
            }

            logger.info("文件上传成功,systemId:{} ,buId:{} ,result:{}",systemId,buId,result);
            List<FileInfo> files = JSON.parseArray(jsonObject.get("data").toString(), FileInfo.class);
            rspResult.setData(files);
            return rspResult;
        } catch (Exception ex) {
            logger.error("FileClient.uploadFile 上传文件异常 ,systemId:{} ,buId:{}",systemId,buId, ex);
            return rspResult.fail("upload file error");
        }

    }

    public CustomResult<List<FileInfo>> batchUploadFile(UploadFileInfo file, List<String> urls) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        try {
            String uploadUrl = String.format("%s/file/doUpload", interfaceConfig.getFrameWorkApiUrl());
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            multipartEntityBuilder.setMode(HttpMultipartMode.RFC6532);
            multipartEntityBuilder.setCharset(Charset.forName("UTF-8"));
            multipartEntityBuilder.setContentType(ContentType.MULTIPART_FORM_DATA);
            multipartEntityBuilder.addTextBody("systemID", String.valueOf(file.getSystemId()));
            multipartEntityBuilder.addTextBody("buID", String.valueOf(file.getBuId()));

            for (String x : urls) {
                UrlResource resource = new UrlResource(x) {
                    @Override
                    public String getFilename() throws IllegalStateException {
                        return FilenameUtils.getName(x);
                    }
                };
                multipartEntityBuilder.addBinaryBody("file", resource.getInputStream(), ContentType.APPLICATION_OCTET_STREAM, resource.getFilename());
            }
            org.apache.http.HttpEntity httpEntity = multipartEntityBuilder.build();
            String result = HttpClientUtil.sendPost(uploadUrl, httpEntity);
            if (StringUtils.isBlank(result)) {
                return rspResult.fail("上传文件失败");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            List<FileInfo> files = JSON.parseArray(jsonObject.get("data").toString(), FileInfo.class);
            rspResult.setData(files);
            return rspResult;
        } catch (Exception ex) {
            logger.error("FileClient.batchUploadFile 上传文件异常：", ex);
            return rspResult.fail(String.format("上传文件异常：%s", ex.getMessage()));
        }
    }

    public CustomResult<List<FileInfo>> batchUploadFileByUrl(UploadFileInfo file) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        try {
            String uploadUrl = String.format("%s/file/uploadForUrl", interfaceConfig.getFrameWorkApiUrl());
            Map<String,Object> params= Maps.newHashMap();
            Map<String,Object> urlsValue= Maps.newHashMap();
            params.put("systemID", String.valueOf(file.getSystemId()));
            urlsValue.put("fileUrl", file.getImageUrl());
            urlsValue.put("buID", String.valueOf(file.getBuId()));
            urlsValue.put("locationID", String.valueOf(file.getLocationID()));
            params.put("urls", Arrays.asList(urlsValue));

            String respStr = StringUtils.EMPTY;
            try {
                respStr = HttpClientUtil.postJson(uploadUrl, JSONObject.toJSONString(params));
            } catch (Exception e) {
                logger.error("根据获取file异常", e);
            }
            JSONObject jsonObject = JSONObject.parseObject(respStr);
            List<FileInfo> files = JSON.parseArray(jsonObject.get("result").toString(), FileInfo.class);
            rspResult.setData(files);
            return rspResult;
        } catch (Exception ex) {
            logger.error("FileClient.batchUploadFile 上传文件异常：", ex);
            return rspResult.fail(String.format("上传文件异常：%s", ex.getMessage()));
        }
    }


    public String downloadByCloudID(String cloudId) {
        return generateUrlByCloudID(cloudId,null,null);
    }

    public void downloadFile(String fileUrl, OutputStream outputStream) throws IOException {
        HttpClientUtil.downloadFile(fileUrl, outputStream);
    }

    public String generateUrlByCloudID(String cloudId, String networkType, String expireInMinutes) {
        if (StringUtils.isBlank(cloudId)) {
            return StringUtils.EMPTY;
        }
        String url = String.format("%s/file/downloadByCloudID", interfaceConfig.getFrameWorkApiUrl());
        Map param = Maps.newHashMap();
        param.put("cloudID", cloudId);
        if (Func.isNotEmpty(networkType)) {
            param.put("networkType", networkType);
        }
        if (Func.isNotEmpty(expireInMinutes)) {
            param.put("expiryTime", expireInMinutes);
        }
        String fileUrl = HttpClientUtil.postFormData(url, param);
        logger.info("FrameWork.file/downloadByCloudID 根据cloudId获取报告文件地址,result:{}", fileUrl);
        return fileUrl;
    }

    /**
     * 根据fileId获取file对象
     * @param fileId
     * @return
     */
    public String getFramewordFileByFileId(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return null;
        }
        String url = interfaceConfig.getFrameWorkApiUrl() + "/file/query";

        Map<String,Object> params= Maps.newHashMap();
        Map<String,Object> value= Maps.newHashMap();
        params.put("queryParams", value);
        value.put("id", fileId);

        String respStr = StringUtils.EMPTY;
        try {
            respStr = HttpClientUtil.postJson(url, JSONObject.toJSONString(params));
        } catch (Exception e) {
            logger.error("根据fileId="+fileId+"+获取file异常", e);
        }

        if(StringUtils.isBlank(respStr)) {
            return null;
        }

        List<FileDTO> fileDTOS = JSONArray.parseArray(respStr, FileDTO.class);
        if(CollectionUtils.isEmpty(fileDTOS)){
            return null;
        }
        FileDTO fileDTO = fileDTOS.get(0);

        return fileDTO.getPath();
    }

    @PreDestroy
    public void shutdown() {
        executor.shutdown();
    }

    /**
     * @param file
     * @return
     */
    public CustomResult copyFile(FileInfo file) {
        CustomResult rspResult = new CustomResult();
        try {
            HashMap<String, Object> reqParams = new HashMap();
            reqParams.put("id", file.getId());
            reqParams.put("generalOrderID", file.getOrderId());
            reqParams.put("objectID", file.getObjectID());

            String frameWorkApi = String.format("%s/file/copyTbfile", interfaceConfig.getFrameWorkApiUrl());

            HttpResult<FileInfo> fileResult = HttpClientUtil.post(frameWorkApi, reqParams, new TypeReference<HttpResult<FileInfo>>() {
            });
            if (fileResult == null || !fileResult.isSuccess()) {
                return null;
            }
            FileInfo rspFile = fileResult.getResult();
            rspResult.setData(rspFile);
            rspResult.setSuccess(rspFile != null);
        } catch (Exception ex) {
            logger.error("copy文件({})异常：", file.getId(), ex);
            rspResult.setMsg(String.format("copy文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return rspResult;
    }

    public static void main(String[] args) throws UploadException, ExecutionException, InterruptedException {
        ImmutableList<String> testDataList = ImmutableList.of(
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/0b/1698725076620e6744f6ebe8b2dae8b1088db4efce.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/b8/16987250809fd61ed280f15ee72f77309acbb332aa.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/16987250849010f870c55151491ec6686e36cd2456.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/32/1698725088c797db41c2e026d50c09af01ae3781e0.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/84/16987250910d51af8758d7b8bb256f5ad72b8e2398.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/1698725093d50f2665c90d4a3ec65a20bab0c6397e.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/2b/1698725095e53f221f823d49c78bbc7c58e177661c.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/0b/1698725076620e6744f6ebe8b2dae8b1088db4efce.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/b8/16987250809fd61ed280f15ee72f77309acbb332aa.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/16987250849010f870c55151491ec6686e36cd2456.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/32/1698725088c797db41c2e026d50c09af01ae3781e0.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/84/16987250910d51af8758d7b8bb256f5ad72b8e2398.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/1698725093d50f2665c90d4a3ec65a20bab0c6397e.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/2b/1698725095e53f221f823d49c78bbc7c58e177661c.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/0b/1698725076620e6744f6ebe8b2dae8b1088db4efce.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/b8/16987250809fd61ed280f15ee72f77309acbb332aa.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/16987250849010f870c55151491ec6686e36cd2456.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/32/1698725088c797db41c2e026d50c09af01ae3781e0.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/84/16987250910d51af8758d7b8bb256f5ad72b8e2398.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/1698725093d50f2665c90d4a3ec65a20bab0c6397e.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/2b/1698725095e53f221f823d49c78bbc7c58e177661c.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/0b/1698725076620e6744f6ebe8b2dae8b1088db4efce.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/b8/16987250809fd61ed280f15ee72f77309acbb332aa.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/16987250849010f870c55151491ec6686e36cd2456.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/32/1698725088c797db41c2e026d50c09af01ae3781e0.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/84/16987250910d51af8758d7b8bb256f5ad72b8e2398.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/1698725093d50f2665c90d4a3ec65a20bab0c6397e.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/2b/1698725095e53f221f823d49c78bbc7c58e177661c.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/0b/1698725076620e6744f6ebe8b2dae8b1088db4efce.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/b8/16987250809fd61ed280f15ee72f77309acbb332aa.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/16987250849010f870c55151491ec6686e36cd2456.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/32/1698725088c797db41c2e026d50c09af01ae3781e0.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/84/16987250910d51af8758d7b8bb256f5ad72b8e2398.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/e6/1698725093d50f2665c90d4a3ec65a20bab0c6397e.jpg",
                "https://img.ltwebstatic.com/images3_pi/2023/10/31/2b/1698725095e53f221f823d49c78bbc7c58e177661c.jpg"
        );
        InterfaceConfig config = new InterfaceConfig();
        config.setFrameWorkApiUrl("http://cnapp-test.sgs.net/FrameWorkApi");
        FileClient uploader = new FileClient(config);
        StopWatch stopWatch = new StopWatch();

        stopWatch.start("first upload");
        uploadByFrameworkAPI(
                "http://cnapp-test.sgs.net/FrameWorkApi",
                "2",
                testDataList.get(0));
        stopWatch.stop();

        stopWatch.start("seq upload");
        Map<String, CustomResult<FileInfo>> sequence = uploader.sequenceUpload(
                SgsSystem.SODA,
                testDataList.stream()
                        .map(url -> Pair.of(UUID.randomUUID().toString(), url))
                        .collect(Collectors.toMap(Pair::getLeft, Pair::getRight))
        );
        System.out.println(sequence);
        stopWatch.stop();

        stopWatch.start("completable upload");
        Map<String, CustomResult<FileInfo>> parallel = uploader.parallelUpload(
                SgsSystem.SODA,
                testDataList.stream()
                        .map(url -> Pair.of(UUID.randomUUID().toString(), url))
                        .collect(Collectors.toMap(Pair::getLeft, Pair::getRight))
        );
        System.out.println(parallel);
        stopWatch.stop();

        stopWatch.start("completable upload for list");
        List<CustomResult<FileInfo>> parallelForList = uploader.parallelUpload(
                SgsSystem.SODA,
                testDataList
        );
        System.out.println(parallelForList);
        stopWatch.stop();

        for (StopWatch.TaskInfo taskInfo : stopWatch.getTaskInfo()) {
            System.out.println(taskInfo.getTaskName() + "\t" + taskInfo.getTimeSeconds()+"/s");
        }
        uploader.shutdown();
    }
}
