package com.sgs.customerbiz.biz.service.todolist;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.service.importtrf.AfterConvertProcessor;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.core.util.NumberUtil;
import com.sgs.customerbiz.core.util.StringUtil;
import com.sgs.customerbiz.core.util.UserHelper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.BoundTrfRelExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.facade.model.enums.CreateType;
import com.sgs.customerbiz.facade.model.file.FileInfo;
import com.sgs.customerbiz.facade.model.order.*;
import com.sgs.customerbiz.facade.model.req.ConvertDataReq;
import com.sgs.customerbiz.facade.model.req.GetRefNoReq;
import com.sgs.customerbiz.facade.model.req.OrderImportTrfReq;
import com.sgs.customerbiz.facade.model.req.TrfInfoReq;
import com.sgs.customerbiz.facade.model.rsp.trforder.*;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfContentDTO;
import com.sgs.customerbiz.facade.model.todolist.req.SearchTrfNoReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.*;
import com.sgs.customerbiz.integration.dto.ContactAddressPO;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfDTOToOrderBody;
import com.sgs.customerbiz.model.trf.dto.TrfLabContactDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.CustomerListQueryReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.ProductLineAbbrEnum;
import com.sgs.preorder.core.order.dto.SysHeaderDto;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.trimslocal.facade.model.enums.ProductLineTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.*;

@Service
public class CustomerOrderService {
    private static final Logger logger = LoggerFactory.getLogger(CustomerOrderService.class);
    @Autowired
    private TrfInfoExtMapper trfInfoExtMapper;
    @Autowired
    private LocalILayerClient iLayerClient;
    @Autowired
    private UserClient userClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private BoundTrfRelExtMapper boundTrfRelExtMapper;
    @Autowired
    private CustomerConfigClient customerConfigClient;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private TrfService trfService;
    @Autowired
    private SciTrfBizService sciTrfBizService;

    @Resource
    private FrameWorkClient frameWorkClient;
    @Autowired
    private TodoListService todoListService;

    @Resource
    protected CustomerTrfDomainService customerTrfDomainService;

    @Resource
    private TrfDomainService trfDomainService;

    @Autowired
    private AfterConvertProcessor afterConvertProcessor;

    @Autowired
    private ConfigClient configClient;

    /**
     * 通过trf 获取 开单准备数据
     *
     * @return
     */
    public CustomResult<OrderDetailDto> mergeCustomerTrfOrderInfo(GetRefNoReq reqObject) {
        logger.info("mergeCustomerTrfOrderInfo request={}", JSON.toJSONString(reqObject));
        CustomResult<OrderDetailDto> rspResult = new CustomResult();
        if (reqObject == null || CollectionUtils.isEmpty(reqObject.getTrfInfos()) || NumberUtil.toInt(reqObject.getRefSystemId()) == 0) {
            return rspResult.fail("PLEASE CHECK trfInfos！");
        }
        Integer refSystemId = reqObject.getRefSystemId();
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(refSystemId);
        if (refSystem == null) {
            return rspResult.fail("PLEASE CHECK refSystemId！");
        }
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        CustomerConfigRsp conf = customerConfigClient.getCustomerConfig(refSystemId, productLineCode);
        if (conf == null) {
            return rspResult.fail(String.format("Get RefSystemId(%s) Config Fail!", refSystemId));
        }
        List<String> trfNoList = reqObject.getTrfInfos().stream().map(TrfInfoReq::getTrfNo).collect(Collectors.toList());
        List<CustomerTrfInfoRsp> custTrfInfoList = trfInfoExtMapper.getCustTrfInfoList(trfNoList, true, new ArrayList<>());
        if (Func.isNotEmpty(custTrfInfoList)) {
            Map<String, String> trfNoMap = new HashMap<>();
            for (CustomerTrfInfoRsp trfInfoRsp : custTrfInfoList) {
                trfNoMap.put(StringUtil.lowerCase(trfInfoRsp.getTrfNo()), trfInfoRsp.getTrfNo());
            }
            reqObject.getTrfInfos().forEach(
                    l -> l.setTrfNo(trfNoMap.get(StringUtil.lowerCase(l.getTrfNo())))
            );
        }


        //check customerRule ，sample是否选择
        CustomResult checkResult = this.checkSampleSelect(reqObject);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        Object rule = checkResult.getData();
        Integer ruleConfig = null;
        if (rule != null) {
            ruleConfig = NumberUtil.toInt(rule.toString());
        }


        // 根据入参 获取数据
        Map<String, List<TrfInfoReq>> trfInfoMaps = reqObject.getTrfInfos().stream().collect(Collectors.groupingBy(TrfInfoReq::getTrfNo));


        // POSL-4666 防止前端sampleNo 未传
        AtomicBoolean needAddSampleNo = new AtomicBoolean(false);
        trfInfoMaps.forEach((key, value) -> {
            if (value.stream().anyMatch(item -> StringUtils.isBlank(item.getSampleNo()))) {
                needAddSampleNo.set(true);
            }
        });
        if (needAddSampleNo.get()) {
            int preSampleNo = 1;
            for (String trfNo : trfInfoMaps.keySet()) {
                List<TrfInfoReq> trfInfoReqs = trfInfoMaps.get(trfNo);
                for (TrfInfoReq trfInfo : trfInfoReqs) {
                    trfInfo.setSampleNo(NumberUtil.numberToLetter(preSampleNo));
                    preSampleNo++;
                }
            }
        }
        // POSL-5186 添加sampleNo 排序
        reqObject.getTrfInfos().sort(Comparator.comparing(TrfInfoReq::getSampleNo));

        Map<String, List<String>> trfSampleNoMaps = Maps.newHashMap();
        for (String trfNo : trfInfoMaps.keySet()) {
            List<TrfInfoReq> trfInfoReqs = trfInfoMaps.get(trfNo);
            for (TrfInfoReq item : trfInfoReqs) {
                // 入参中 需要的 component, 如果不为空，需要指定 component 数据
                if (!CollectionUtils.isEmpty(item.getComponentIds())) {
                    item.setTestSampleIds(item.getComponentIds());
                }

                if (!trfSampleNoMaps.containsKey(trfNo)) {
                    trfSampleNoMaps.put(trfNo, Lists.newArrayList(item.getSampleNo()));
                    continue;
                }
                List<String> hasTrfNo = trfSampleNoMaps.get(trfNo);
                if (!hasTrfNo.contains(item.getSampleNo())) {
                    hasTrfNo.add(item.getSampleNo());
                }
                trfSampleNoMaps.put(trfNo, hasTrfNo);
            }
        }
        // 获取数据库中保存的trf信息
        List<CustomerTrfInfoRsp> trfs = trfInfoExtMapper.getCustTrfInfoList(Lists.newArrayList(trfInfoMaps.keySet()), true, Lists.newArrayList());
        if (CollectionUtils.isEmpty(trfs) || !NumberUtil.equals(trfInfoMaps.size(), trfs.size())) {
            return rspResult.fail("请检查TrfNo是否都已导入系统或LabCode是否匹配或是否已取消！");
        }
        com.sgs.preorder.facade.model.info.user.UserLabBuInfo userLabBu = userClient.getUserLabBuInfo();
        String labCode;

        if (Func.isEmpty(userLabBu) && Func.isNotBlank(reqObject.getLabCode())) {
            labCode = reqObject.getLabCode();
        } else if (Func.isNotEmpty(userLabBu)) {
            labCode = userLabBu.getLabCode();
        } else {
            labCode = null;
        }

        if (Func.isBlank(labCode)) {
            return rspResult.fail("请检查LabCode信息不能为空！");
        }

        // 增加lab校验
        long count = trfs.stream().filter(l -> !Objects.equals(labCode, l.getLabCode())).count();
        if (count > 0) {
            return rspResult.fail("请检查TrfNo LabCode是否匹配！");
        }

        // 按照请求的顺序排序
        List<CustomerTrfInfoRsp> customerTrfInfo = Lists.newArrayList();
        for (TrfInfoReq req : reqObject.getTrfInfos()) {
            CustomerTrfInfoRsp trf = trfs.stream().filter(info -> StringUtils.equalsIgnoreCase(info.getTrfNo(), req.getTrfNo())).findFirst().orElse(null);
            if (trf == null) {
                return rspResult.fail(String.format("获取 %s 信息失败，请检查数据是否import", req.getTrfNo()));
            }
            if (TrfStatusEnum.check(trf.getTrfStatus(), TrfStatusEnum.Canceled)) {
                return rspResult.fail(String.format("当前TrfNo(%s)已被Canceled！", trf.getTrfNo()));
            }
            customerTrfInfo.add(trf);
        }
        // 希音校验
        switch (refSystem) {
            case Shein:
            case SheinSupplier:
                List<TrfContentDTO> trfCreateType = trfInfoExtMapper.getCustTrfContentInfo(Lists.newArrayList(trfInfoMaps.keySet()), null);
                if (CollectionUtils.isEmpty(trfCreateType)) {
                    return rspResult.fail("获取Trf 信息(CreateType)失败，请检查数据");
                }
                Set<Integer> createTypeSets = trfCreateType.stream().map(TrfContentDTO::getCreateType).collect(Collectors.toSet());
                if (createTypeSets.contains(CreateType.CreateTypeSelf.getType()) && createTypeSets.contains(CreateType.CreateTypeOther.getType())) {
                    return rspResult.fail("SHEIN订单不能同时选择自建和非自建TrfNo，请重新选择！");
                }
                List<TrfOrderPO> boundTrfInfo = boundTrfRelExtMapper.getBoundTrfInfoList(Lists.newArrayList(trfInfoMaps.keySet()), BoundStatus.BoundHasOrder.getType());
                if (!CollectionUtils.isEmpty(boundTrfInfo)) {
                    List<String> trfNoLists = boundTrfInfo.stream().map(TrfOrderPO::getTrfNo).collect(Collectors.toList());
                    return rspResult.fail(String.format("trfNo:(%s)已绑定，请重新选择！", StringUtil.join(trfNoLists, ",")));
                }
                // SL非自建单必须带component
                // modify by vincent 2023年6月2日 增加根据配置规则进行check，只有配置规则为1时 进行check
                if (NumberUtil.equals(ruleConfig, 1) &&
                        createTypeSets.contains(CreateType.CreateTypeOther.getType())
                        && StringUtils.equalsIgnoreCase(ProductLineType.SL.getProductLineAbbr(), ProductLineContextHolder.getProductLineCode())) {
                    List<String> errorTrfNos = Lists.newArrayList();
                    for (TrfInfoReq trf : reqObject.getTrfInfos()) {
                        if (CollectionUtils.isEmpty(trf.getTestSampleIds())) {
                            errorTrfNos.add(trf.getTrfNo());
                        }
                    }
                    if (!CollectionUtils.isEmpty(errorTrfNos)) {
                        return rspResult.fail(String.format("trfNo:(%s) is not select material , please check your selection", StringUtil.join(errorTrfNos, ",")));
                    }
                }
                break;
        }
        // <trfNo, Content>
        Map<String, String> trfContentMaps = customerTrfInfo.stream().collect(Collectors.toMap(CustomerTrfInfoRsp::getTrfNo, CustomerTrfInfoRsp::getContent, (k1, k2) -> k1));

        Optional<CustomerGeneralConfig> customerConfig = configClient.getCustomerConfigBy(refSystemId);
        // 拼装 请求iLayer 的请求参数
        CustomResult<List<Object>> trfDataRsp = this.buildTrfData(refSystem, trfInfoMaps, trfContentMaps, reqObject.getTrfInfos(),customerConfig);
        if (!trfDataRsp.isSuccess()) {
            return rspResult.fail(trfDataRsp.getMsg());
        }
        List<Object> trfDatas = trfDataRsp.getData();

        // 请求 iLayer 获取 新结构
        ConvertDataReq reqData = new ConvertDataReq();
        reqData.setCustomerGroupCode(conf.getCustomerGroupCode());
        reqData.setProductLineId(ProductLineContextHolder.getProductLineId());
        reqData.setSceneTypes(Lists.newArrayList(CREATE_ORDERBY_TRF));

        SciRequestContext sciRequestContext = ProductLineContextHolder.retrieveSciRequestContext();
        if (Func.isNotEmpty(sciRequestContext) && Func.isNotEmpty(sciRequestContext.getSystemId())) {
            reqData.setRefSystemId(sciRequestContext.getSystemId());
        } else {
            Boolean isIntegrationSGSMart = customerConfig.map(CustomerGeneralConfig::isIntegratedSGSMart).orElse(false);
            // TODO SCI-900
            if (isIntegrationSGSMart) {
                iLayerClient.rewriteRefSystemId(reqData, 10023, "CG0000219");
            } else {
                if (Objects.equals(refSystemId, 2)) {
                    refSystemId = 1;
                }
                reqData.setRefSystemId(refSystemId);
            }
        }

        JsonNode jsonNode = iLayerClient.convertData(reqData, trfDatas);
        if (jsonNode == null) {
            return rspResult.fail("获取TRF信息失败，请检查数据！");
        }

        TrfOrderRsp trfOrderRsp = JSONObject.parseObject(jsonNode.toString(), TrfOrderRsp.class);

        CustomResult<OrderDetailDto> orderBaseRsp = this.getOrderDetailInfo(reqObject);
        if (!orderBaseRsp.isSuccess()) {
            return rspResult.fail(orderBaseRsp.getMsg());
        }
        OrderDetailDto orderDetailInfo = orderBaseRsp.getData();

        // 设置Headers
        this.buildHeaders(orderDetailInfo.getHeaders(), trfOrderRsp, trfs);

        // 设置 CustomerInfo
        this.buildCustomerInfo(orderDetailInfo, trfOrderRsp, refSystem);

        // 设置 TestRequest 和报告语言
        this.buildTestRequest(orderDetailInfo, trfOrderRsp.getTestRequest());
        String languageCode = orderDetailInfo.getTestRequest().getReportLanguage();
        ReportLanguage reportLanguage = ReportLanguage.findName(languageCode);
        // 设置语言
        if (reportLanguage != null) {
            orderDetailInfo.setLanguageID(reportLanguage.getLanguageId());
        }

        // 设置原样 子样等信息
        this.buildDffSampleInfo(trfOrderRsp, orderDetailInfo, trfSampleNoMaps, languageCode, refSystem, productLineCode);

        // 设置TestLine信息
        this.buildTestLineInfo(trfOrderRsp, orderDetailInfo);
        //设置附件信息
        this.buildAttachment(trfOrderRsp, orderDetailInfo);

        // 设置图片
        this.buildPicture(reqObject, orderDetailInfo);

        // 补充信息
        Map<String, String> trfObjectNoMap = customerTrfInfo.stream()
                .filter(item -> StringUtils.isNotBlank(item.getTrfNo()) && StringUtils.isNotBlank(item.getObjectNo()))
                .collect(Collectors.toMap(CustomerTrfInfoRsp::getTrfNo, CustomerTrfInfoRsp::getObjectNo, (k1, k2) -> k1));

        // 设置Header 中的 setReference结构
//        this.buildDetailSupplement(trfOrderRsp, orderDetailInfo, trfObjectNoMap, refSystemId);

        orderDetailInfo.setOther(new OrderDetailOtherInfo());  // 是否需要???

        OrderHeaderDTO headers = orderDetailInfo.getHeaders();
        headers.setReferenceId(refSystem.getRefSystemId());
        // Customer特殊处理
        switch (refSystem) {
            case Shein:
                //希音设置 Remark
                List<String> batchNos = customerTrfInfo.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getBatchNo()))
                        .map(CustomerTrfInfoRsp::getBatchNo).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(batchNos)) {
                    orderDetailInfo.getHeaders().setRemark(String.format("[BatchNo]:%s", StringUtils.join(batchNos, ",")));
                }
                // 只有SL 需要 BatchNo
                if (!StringUtils.equalsIgnoreCase(ProductLineType.SL.getProductLineAbbr(), ProductLineContextHolder.getProductLineCode())) {
                    orderDetailInfo.getHeaders().setRemark(null);
                }
                if (!CollectionUtils.isEmpty(trfOrderRsp.getProductSamples())) {
                    List<TrfProductSampleRsp> trfProductSamples = trfOrderRsp.getProductSamples();
                    Map<String, String> trfCreateTypeMap = trfProductSamples.stream()
                            .collect(Collectors.toMap(TrfProductSampleRsp::getReferenceNo, TrfProductSampleRsp::getCreateType, (k1, k2) -> k2));
                    List<ReferenceInfo> references = Lists.newArrayList();

                    List<ProductSampleInfo> productSamples = orderDetailInfo.getProductSamples();
                    Map<String, String> trfExtObjectMap = Maps.newHashMap();
                    if (!CollectionUtils.isEmpty(productSamples)) {
                        trfExtObjectMap = productSamples.stream()
                                .filter(item -> !StringUtils.isBlank(item.getExtObjectId()))
                                .collect(Collectors.toMap(ProductSampleInfo::getReferenceNo, ProductSampleInfo::getExtObjectId, (k1, k2) -> k2));
                    }
                    Map<String, ReferenceInfo> refMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(headers.getReferences())) {
                        for (ReferenceInfo reference : headers.getReferences()) {
                            refMap.put(reference.getReferenceNo(), reference);
                        }
                    }
                    for (String trfNo : trfCreateTypeMap.keySet()) {
                        String createType = trfCreateTypeMap.get(trfNo);
                        ReferenceInfo finalRef = new ReferenceInfo();
                        finalRef.setReferenceNo(trfNo);
                        finalRef.setCreateType(NumberUtil.toInt(createType));
                        finalRef.setPackageBarcode(trfObjectNoMap.get(trfNo));
                        finalRef.setExtObjectId(trfExtObjectMap.get(trfNo));
                        ReferenceInfo refExt = refMap.get(trfNo);
                        if (Objects.nonNull(refExt)) {
                            finalRef.setExtData(refExt.getExtData());
                        }
                        references.add(finalRef);
                    }
                    headers.setReferenceNo(StringUtil.join(Lists.newArrayList(trfCreateTypeMap.keySet()), ","));

                    headers.setReferences(references);
                }
                break;
            case SheinSupplier:
                //希音设置 Remark
                List<String> batchNoSuppliers = customerTrfInfo.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getBatchNo()))
                        .map(CustomerTrfInfoRsp::getBatchNo).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(batchNoSuppliers)) {
                    orderDetailInfo.getHeaders().setRemark(String.format("[BatchNo]:%s", StringUtils.join(batchNoSuppliers, ",")));
                }
                List<TrfProductSampleRsp> productSamples = trfOrderRsp.getProductSamples();
                // 只有SL 需要 BatchNo
                if (!StringUtils.equalsIgnoreCase(ProductLineType.SL.getProductLineAbbr(), ProductLineContextHolder.getProductLineCode())) {
                    orderDetailInfo.getHeaders().setRemark(null);
                    productSamples = trfOrderRsp.getProductSampleRspList().get(0).getProductSamples();
                }
                Map<String, ReferenceInfo> refMap = new HashMap<>();
                if (CollUtil.isNotEmpty(headers.getReferences())) {
                    for (ReferenceInfo reference : headers.getReferences()) {
                        refMap.put(reference.getReferenceNo(), reference);
                    }
                }
                if (!CollectionUtils.isEmpty(productSamples)) {
                    List<ReferenceInfo> references = Lists.newArrayList();
                    Set<String> trfNos = Sets.newHashSet();
                    for (TrfProductSampleRsp productSampleRsp : productSamples) {
                        if (trfNos.contains(productSampleRsp.getReferenceNo())) {
                            continue;
                        }
                        trfNos.add(productSampleRsp.getReferenceNo());
                        ReferenceInfo referenceInfo = new ReferenceInfo();
                        referenceInfo.setReferenceNo(productSampleRsp.getReferenceNo());
                        referenceInfo.setCreateType(NumberUtil.toInt(productSampleRsp.getCreateType()));
                        referenceInfo.setPackageBarcode(trfObjectNoMap.get(productSampleRsp.getReferenceNo()));
                        referenceInfo.setExtObjectId(productSampleRsp.getExtObjectId());
                        ReferenceInfo refExt = refMap.get(productSampleRsp.getReferenceNo());
                        if (Objects.nonNull(refExt)) {
                            referenceInfo.setExtData(refExt.getExtData());
                        }
                        references.add(referenceInfo);
                    }
                    List<String> collect = references.stream().map(ReferenceInfo::getReferenceNo).distinct().collect(Collectors.toList());
                    headers.setReferenceNo(StringUtil.join(collect, ","));
                    headers.setReferences(references);
                }
                break;
            case SGSMart:
            case F21:
            case JO_ANN:
            case Walmart:
            case Walmart_Group:
            case Target:
            case BigLots:
            case DollarTree:
            case Veyer:
            case Amazon:
            case LOWES:
            case TARGET_INSPECTORIO:
                if (Func.isEmpty(headers.getReferences())
                        && Func.isNotEmpty(headers.getReferenceNo())) {
                    headers.setReferenceNo(headers.getReferenceNo());
                    ReferenceInfo ref = new ReferenceInfo();
                    ref.setReferenceNo(headers.getReferenceNo());
                    headers.setReferences(Lists.newArrayList(ref));
                }
                break;
            case ANTA:
            case LINING:
            case Camel:
            case PeaceBird:
            case SEMIR:
            case FastFish:
            case Septwolves:
            case TIC:
                ReferenceInfo referenceInfo = new ReferenceInfo();
                referenceInfo.setReferenceNo(StringUtils.defaultString(trfOrderRsp.getHeaders().getTrfNo(), trfOrderRsp.getHeaders().getReferenceNo()));
                headers.setReferenceNo(referenceInfo.getReferenceNo());
                CustomerTrfInfoRsp customerTrfInfoRsp = trfs.stream().filter(l -> Objects.equals(l.getTrfNo(), referenceInfo.getReferenceNo())).findFirst().orElse(new CustomerTrfInfoRsp());
                referenceInfo.setTrfReportLevel(customerTrfInfoRsp.getTrfReportLevel());
                headers.setReferences(Lists.newArrayList(referenceInfo));
                break;
            default:
                break;
        }

        rspResult.setData(orderDetailInfo);
        rspResult.setSuccess(true);
        return rspResult;

    }

    /**
     * merge数据前，先cehck 数据规则
     *
     * @param req
     * @return
     */
    private CustomResult<Integer> checkSampleSelect(GetRefNoReq req) {
        CustomResult result = new CustomResult();

        SearchTrfNoReq searchTrfNoReq = new SearchTrfNoReq();
        List<TrfInfoReq> trfInfos = req.getTrfInfos();
        //这个方法不会对req里面的trfInfo进行check ，因为主方法已经check过，如果有调用这个方法，一定要自己check好数据
        TrfInfoReq trfInfoReq = trfInfos.get(0);
        String firstTrfNo = trfInfoReq.getTrfNo();
        searchTrfNoReq.setTrfNo(firstTrfNo);
        searchTrfNoReq.setRefSystemId(req.getRefSystemId());
        searchTrfNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        CustomResult<Integer> customResult = todoListService.checkCustomerRule(searchTrfNoReq);
        if (!customResult.isSuccess()) {
            return customResult;
        }
        Integer data = customResult.getData();
        int rule = NumberUtil.toInt(data);
        //0的话，不用check ，因为选不选都可以，1的话，每一个trf都必须要check
        if (NumberUtil.equals(rule, 0)) {
            return CustomResult.newSuccessInstance();
        }
        Set<String> errorMsg = Sets.newHashSet();
        Map<String, List<TrfInfoReq>> trfInfoMaps = trfInfos.stream().collect(Collectors.groupingBy(TrfInfoReq::getTrfNo));
        trfInfoMaps.forEach((trfNo, reqs) -> {
            boolean hasSample = false;
            for (TrfInfoReq infoReq : reqs) {
                List<String> testSampleIds = infoReq.getTestSampleIds();
                hasSample = (!CollectionUtils.isEmpty(testSampleIds)) || hasSample;
            }
            if (!hasSample) {
                errorMsg.add(String.format("TRFNo:%s must be select at least one product", trfNo));
            }
        });
        result.setSuccess(CollectionUtils.isEmpty(errorMsg));
        result.setMsg(StringUtils.join(errorMsg, ";"));
        result.setData(rule);
        return result;
    }

    private void assembleReferenceExtData(ReferenceInfo referenceInfo, String content) {


    }

    /**
     * 拼装 请求iLayer 的请求参数
     *
     * @param refSystem
     * @param trfInfoMaps
     * @param trfContentMaps
     * @return
     */
    private CustomResult<List<Object>> buildTrfData(RefSystemIdEnum refSystem, Map<String, List<TrfInfoReq>> trfInfoMaps,
                                                    Map<String, String> trfContentMaps,
                                                    List<TrfInfoReq> trfInfos,
                                                    Optional<CustomerGeneralConfig> customerConfig) {
        CustomResult<List<Object>> rspResult = new CustomResult();

        List<Object> trfs = Lists.newArrayList();
        Map<String, Object> trfNoMaps = Maps.newHashMap();
        for (String trfNo : trfInfoMaps.keySet()) {
            List<TrfInfoReq> trfInfoReqs = trfInfoMaps.get(trfNo);
            if (!trfContentMaps.containsKey(trfNo)) {
                return rspResult.fail("请检查TrfNo是否都已导入系统或LabCode是否匹配或是否已取消！");
            }
            // POSL-5186 添加sampleNo 排序
            trfInfoReqs.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getSampleNo())).collect(Collectors.toList())
                    .sort(Comparator.comparing(TrfInfoReq::getSampleNo));
            TrfInfoReq trfReq = trfInfoReqs.stream().filter(info -> StringUtils.equalsIgnoreCase(info.getTrfNo(), trfNo)).findFirst().orElse(null);
            if (trfReq == null) {
                continue;
            }
            if (!StringUtils.equalsIgnoreCase(trfReq.getTrfNo(), trfNo)) {
                continue;
            }

            String content = trfContentMaps.get(trfNo);
            Set<String> testSampleIds = Sets.newHashSet();
            Map<String, String> sampleIdNos = Maps.newHashMap();
            for (TrfInfoReq trf : trfInfoReqs) {
                if (CollectionUtils.isEmpty(trf.getTestSampleIds())) {
                    continue;
                }
                testSampleIds.addAll(trf.getTestSampleIds());
                trf.getTestSampleIds().forEach(item -> {
                    sampleIdNos.put(item, trf.getSampleNo());
                });
            }
            // POSL-5117 希音 希音供应商 如果不传入 sample，  则不需要sample
            // 希音 希音供应商之外的场景 显示所有的
            if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.Shein, RefSystemIdEnum.SheinSupplier, RefSystemIdEnum.TIC)) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    JsonNode jsonNode = objectMapper.readTree(content);
                    ObjectNode objectNode = jsonNode.deepCopy();
                    JsonNode jsonNodeList = objectNode.get(refSystem.getSampleNode());
                    if (jsonNodeList != null && jsonNodeList.isArray()) {
                        Iterator<JsonNode> it = jsonNodeList.iterator();
                        List<ObjectNode> objectNodes = Lists.newArrayList();
                        while (it.hasNext()) {
                            JsonNode childNode = it.next();
                            ObjectNode objectItem = childNode.deepCopy();
                            if (RefSystemIdEnum.check(refSystem.getRefSystemId(),RefSystemIdEnum.TIC)) {
                                objectItem.put(LIST_ROW_ID_HIDE, objectItem.get(SAMPLE_NO));
                            }
                            JsonNode idJsonNode = objectItem.get(LIST_ROW_ID_HIDE);
                            // 入参中 不包含 componentId，此条记录不给Ilayer
                            if (idJsonNode == null || !testSampleIds.contains(idJsonNode.asText())) {
                                continue;
                            }
                            objectItem.put(SAMPLE_HIDE, sampleIdNos.get(idJsonNode.asText()));
                            objectNodes.add(objectItem);
                        }
                        objectNode.set(refSystem.getSampleNode(), objectMapper.readTree(objectNodes.toString()));
                    }

                    if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.Shein)) {
                        JsonNode goodsPictureUrlListNode = objectNode.get(GOODS_PICTURE_URL_LIST);
                        if (Func.isEmpty(goodsPictureUrlListNode)) {
                            if (goodsPictureUrlListNode == null) {
                                List<ObjectNode> objectNodes = Lists.newArrayList();
                                JsonNode node = objectNode.get(GOODS_PICTURE_URL);
                                if (Func.isNotEmpty(node)) {
                                    objectNodes.add(node.deepCopy());
                                }
                                objectNode.put(GOODS_PICTURE_URL_LIST, objectMapper.readTree(objectNodes.toString()));
                            }
                        }
                        if (Func.isEmpty(objectNode.get(GOODS_COLOR))) {
                            objectNode.put(GOODS_COLOR, "");
                        } else {
                            objectNode.put(GOODS_COLOR, objectNode.get(GOODS_COLOR));
                        }
                    }
                    content = objectNode.toString();
                } catch (Exception e) {
                    logger.error(String.format("请检查TrfNo(%s)的Content数据，转换失败", trfNo));
                    return rspResult.fail("请检查TrfNo(%s)的Content数据，转换失败");
                }
            }
            Boolean usingSGSTrfToOrder = customerConfig.map(CustomerGeneralConfig::usingSGSTrfToOrder).orElse(false);
            if (usingSGSTrfToOrder) {
                logger.info("using SgsTrf to order. refSystem: {} trfNo {}", refSystem, trfNo);
                TrfDTO trfDTO = customerTrfContentToSGSTrf(refSystem.getRefSystemId(), content);
                afterConvertProcessor.processDffValue(refSystem.getRefSystemId(), trfDTO);
                trfNoMaps.put(trfNo, TrfDTOToOrderBody.valueOf(trfDTO));
            } else {
                trfNoMaps.put(trfNo, JSON.parse(content));
            }
//            trfs.add(JSON.parse(content));
        }
        // 排序
        for (TrfInfoReq trfInfoReq : trfInfos) {
            if (trfNoMaps.containsKey(trfInfoReq.getTrfNo())) {
                trfs.add(trfNoMaps.get(trfInfoReq.getTrfNo()));
            }
        }

        rspResult.setData(trfs);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public TrfDTO customerTrfContentToSGSTrf(Integer refSystemId, String content) {
        JSONObject contentObject = JSON.parseObject(content);
        Integer systemId = (Integer) Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.systemId")).orElse(null);
        String labCode = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labCode")).map(Object::toString).orElse(null);
        String buCode = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.buCode")).map(Object::toString).orElse(null);
        String templateId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.trfTemplateId")).map(Object::toString).orElse(null);
        String templateType = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.trfTemplateType")).map(Object::toString).orElse(null);
        String formId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.formId")).map(Object::toString).orElse(null);
        String gridId = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.gridId")).map(Object::toString).orElse(null);
        String labContactName = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labContactName")).map(Object::toString).orElse(null);
        TrfLabContactDTO labContact = Optional.ofNullable(JSONPath.eval(contentObject, "$.__sgs_platform_request_context.labContact"))
                .filter(json -> (json instanceof JSONObject))
                .map(json -> (JSONObject) json)
                .map(obj -> JSON.parseObject(obj.toJSONString(), TrfLabContactDTO.class))
                .orElse(null);
        TrfDTO trfDTO = customerTrfDomainService.convert(content, refSystemId, systemId, labCode, buCode, templateId, templateType, formId, gridId, labContact);
        afterConvertProcessor.processDffCodeMapping(refSystemId, formId, trfDTO);
        return trfDTO;
    }

    /**
     * 设置订单基础数据
     *
     * @return
     */
    private CustomResult<OrderDetailDto> getOrderDetailInfo(GetRefNoReq reqObject) {
        CustomResult<OrderDetailDto> rspResult = new CustomResult();

        OrderDetailDto orderDetail = new OrderDetailDto();

        UserInfo user = UserHelper.getLocalUser();

        com.sgs.preorder.facade.model.info.user.UserLabBuInfo userLabBu = userClient.getUserLabBuInfo();
        String labCode = reqObject.getLabCode();
        if (Objects.isNull(userLabBu) && Objects.nonNull(labCode)) {
            LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(labCode);
            userLabBu = convertToUserLabBU(labInfo);
        }
        UserLabBuInfo lab = new UserLabBuInfo();

        OrderHeaderDTO header = new OrderHeaderDTO();
        if (userLabBu != null) {
            BeanUtils.copyProperties(userLabBu, lab);
            orderDetail.setLab(lab);

            header.setOrganizationId(userLabBu.getOrganizationID());
            header.setOrganizationName(userLabBu.getOrganizationName());
            header.setLegalEntityCode(userLabBu.getLegalEntityCode());
            header.setLegalEntityName(userLabBu.getLegalEntityName());
            header.setLabName(userLabBu.getLabName());
            header.setPostfixItems(userLabBu.getPostfix());

            // DIG-8555
            header.setLabId(userLabBu.getLabId());
            header.setLabCode(userLabBu.getLabCode());
            header.setLocationId(userLabBu.getLocationId());
            header.setLocationCode(userLabBu.getLocationCode());
            header.setBuId(userLabBu.getProductLineId());
            header.setBuCode(userLabBu.getProductLineCode());
            header.setResponsibleTeamCode(userLabBu.getTeamName());
        }
        header.setNewOrderId(UUID.randomUUID().toString());
        Optional.ofNullable(user).map(UserInfo::getRegionAccount).ifPresent(header::setcSName);
        Optional.ofNullable(user).map(UserInfo::getEmail).ifPresent(header::setcSEmail);
        Optional.ofNullable(user).map(UserInfo::getTelephone).ifPresent(header::setcSContact);
        // DIG-8555 A "NullPointerException" could be thrown; "userLabBu" is nullable here.
        /*header.setLabId(userLabBu.getLabId());
        header.setLabCode(userLabBu.getLabCode());

        header.setLocationId(userLabBu.getLocationId());
        header.setLocationCode(userLabBu.getLocationCode());

        header.setBuId(userLabBu.getProductLineId());
        header.setBuCode(userLabBu.getProductLineCode());

        // header.setResponsibleTeamCode(userLabBu.getTeamCode());
        header.setResponsibleTeamCode(userLabBu.getTeamName());*/
        //boolean dataException = this.checkHeaderData(header, user, "开单");
        orderDetail.setHeaders(header);

        rspResult.setData(orderDetail);
        rspResult.setSuccess(true);
        return rspResult;
    }

    private com.sgs.preorder.facade.model.info.user.UserLabBuInfo convertToUserLabBU(LabInfo labInfo) {
        com.sgs.preorder.facade.model.info.user.UserLabBuInfo userLabBuInfo = new com.sgs.preorder.facade.model.info.user.UserLabBuInfo();
        userLabBuInfo.setLabId(Func.toLongObject(labInfo.getLaboratoryID(), null));
        userLabBuInfo.setLabCode(labInfo.getLaboratoryCode());
        userLabBuInfo.setLabName(labInfo.getLaboratoryName());
        userLabBuInfo.setLabAddress(labInfo.getLaboratoryAddress());
        userLabBuInfo.setLocationId(Func.toInteger(labInfo.getLocationID(), null));
        userLabBuInfo.setLocationCode(labInfo.getLaboratoryCode());
        userLabBuInfo.setLocationName(labInfo.getLaboratoryName());
        userLabBuInfo.setProductLineId(Func.toInteger(labInfo.getProductLineID(), null));
        userLabBuInfo.setProductLineCode(labInfo.getProductLineAbbr());
        userLabBuInfo.setProductLineName(labInfo.getProductLineName());
//        userLabBuInfo.setOrganizationID(labInfo.get);
//        userLabBuInfo.setOrganizationName();
//        userLabBuInfo.setLegalEntityCode(labInfo.get);
//        userLabBuInfo.setLegalEntityName();
//        userLabBuInfo.setProductTypeCode(labInfo.gettype);
//        userLabBuInfo.setPostfix();
//        userLabBuInfo.setCountryCode(labInfo.getco);
//        userLabBuInfo.setTeamCode(labInfo.get);
//        userLabBuInfo.setTeamName();
        return userLabBuInfo;


    }

    /**
     * 设置Header
     *
     * @param headers
     * @param trfOrder
     */
    private void buildHeaders(OrderHeaderDTO headers, TrfOrderRsp trfOrder, List<CustomerTrfInfoRsp> trfs) {
        // 设置默认值
        headers.setQuoteServiceLevel(1);
        headers.setDateEditFlag(1);
        headers.setCaseType("Local");

        // 设置 Header
        OrderHeaderRsp orderHeader = trfOrder.getHeaders();
        if (orderHeader == null) {
            return;
        }
        if (StringUtils.isNotBlank(orderHeader.getPostfix())) {
            headers.setPostfix(orderHeader.getPostfix());
        }
        if (NumberUtil.toInt(orderHeader.getTat()) > 0) {
            headers.setTat(orderHeader.getTat());
        }
        if (NumberUtil.toInt(orderHeader.getServiceLevel()) > 0) {
            headers.setServiceLevel(orderHeader.getServiceLevel());
        } else {
            String buCode = headers.getBuCode();
            if (Objects.equals(buCode, com.sgs.otsnotes.facade.model.enums.ProductLineType.SL.getProductLineAbbr())) {
                headers.setServiceLevel(frameWorkClient.getDefaultServiceType(com.sgs.otsnotes.facade.model.enums.ProductLineType.SL.getProductLineId(), headers.getLocationId()));
            }
        }
        headers.setCustomerRemark(orderHeader.getCustomerRemarks());

        if (StringUtils.isNotBlank(orderHeader.getTrfSubmissionDate())) {
            headers.settRFSubmissionDate(DateUtils.parseDate(orderHeader.getTrfSubmissionDate()));
        }
        if (orderHeader.getSelfTestFlag() != null) {
            headers.setSelfTestFlag(orderHeader.getSelfTestFlag());
        }
        if (StringUtils.isNotBlank(orderHeader.getParcelNo())) {
            headers.setParcelNo(orderHeader.getParcelNo());
        }

        if (Objects.nonNull(orderHeader.getReferences())) {
            List<ReferenceInfo> refList = orderHeader.getReferences().parallelStream().map(referenceInfoRsp ->
                    BeanUtil.copyProperties(referenceInfoRsp, ReferenceInfo.class)
            ).collect(Collectors.toList());
            headers.setReferences(refList);
        }
        if (Func.isEmpty(headers.getReferences()) && Func.isNotEmpty(orderHeader.getReferenceNo())) {
            headers.setReferenceNo(orderHeader.getReferenceNo());
        }
        if (CollUtil.isEmpty(trfs)) {
            return;
        }
        //HL的情况使用imported by
        if (ProductLineAbbrEnum.HL.getCode().equalsIgnoreCase(headers.getBuCode())) {
            CustomerTrfInfoRsp firstTrf = trfs.get(0);
            headers.setcSName(firstTrf.getCreatedBy());
        }


    }

    /**
     * 设置Customer
     *
     * @param orderDetail
     * @param orderRsp
     */
    private void buildCustomerInfo(OrderDetailDto orderDetail, TrfOrderRsp orderRsp, RefSystemIdEnum refSystem) {

        String locationCode = orderDetail.getLab().getLocationCode();
        if (orderRsp.getApplicant() != null && NumberUtil.toLong(orderRsp.getApplicant().getAccountId()) > 0 && NumberUtil.toLong(orderRsp.getApplicant().getNumber()) > 0) {
            orderDetail.setApplicant(this.getNewCustomer(locationCode, CustomerType.Applicant, orderRsp.getApplicant()));
        }

        if (orderRsp.getPayer() != null && NumberUtil.toInt(orderRsp.getPayer().getAccountId()) > 0 && NumberUtil.toInt(orderRsp.getPayer().getNumber()) > 0) {
            orderDetail.setPayer(this.getNewCustomer(locationCode, CustomerType.Payer, orderRsp.getPayer()));
        }

        if (orderRsp.getBuyer() != null && NumberUtil.toInt(orderRsp.getBuyer().getNumber()) > 0) {
            orderDetail.setBuyer(this.getNewCustomer(locationCode, CustomerType.Buyer, orderRsp.getBuyer()));
        }

        if (orderRsp.getAgent() != null && NumberUtil.toInt(orderRsp.getAgent().getAccountId()) > 0 && NumberUtil.toInt(orderRsp.getAgent().getNumber()) > 0) {
            orderDetail.setAgent(this.getNewCustomer(locationCode, CustomerType.Agent, orderRsp.getAgent()));
        }

        // 希音供应商直接赋值
        if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.SheinSupplier, RefSystemIdEnum.PeaceBird, RefSystemIdEnum.TIC)) {
            if (orderRsp.getApplicant() != null) {
                // 希音供应商直接赋值
                CustomerInfo customeApplicant = new CustomerInfo();
                CustomerRsp applicant = orderRsp.getApplicant();
                BeanUtils.copyProperties(applicant, customeApplicant);
                customeApplicant.setCustomerNameCN(applicant.getCustomerNameCn());
                customeApplicant.setCustomerAddressCN(applicant.getAddressCn());
                customeApplicant.setCustomerAddressEN(applicant.getAddressEn());
                customeApplicant.setContactPersonName(applicant.getContactName());
                customeApplicant.setContactPersonPhone1(applicant.getTelephone());
                customeApplicant.setContactPersonEmail(applicant.getEmail());
                customeApplicant.setCustomerUsage(CustomerType.Applicant.getCode());
                orderDetail.setApplicant(customeApplicant);
            }
//            if (orderRsp.getPayer() != null) {
//                // 希音供应商直接赋值
//                CustomerInfo customePayer = new CustomerInfo();
//                CustomerRsp payer = orderRsp.getPayer();
//                BeanUtils.copyProperties(payer, customePayer);
//                customePayer.setCustomerNameCN(payer.getCustomerNameCn());
//                customePayer.setCustomerAddressCN(payer.getAddressCn());
//                customePayer.setCustomerAddressEN(payer.getAddressEn());
//                customePayer.setContactPersonName(payer.getContactName());
//                customePayer.setContactPersonPhone1(payer.getTelephone());
//                customePayer.setContactPersonEmail(payer.getEmail());
//                customePayer.setCustomerUsage(CustomerType.Payer.getCode());
//                orderDetail.setPayer(customePayer);
//            }
        }

        if (refSystem == RefSystemIdEnum.LOWES) {
            // todo fix lowe's 只返回了number，没有英文名，导致gpo 开单的时候，页面上没有展示buyer
            // 这里长期的方案是统一使用groupNo + customerNo 查询。因时间成本限制，先临时更改
            Optional.ofNullable(orderRsp.getBuyer())
                    .filter(buyer -> Objects.nonNull(buyer.getNumber()) && buyer.getNumber().equals(LOWES_CUSTOMER_NO))
                    .ifPresent(buyer -> {
                        buyer.setCustomerNameEn(LOWES_NAME_OF_CUSTOMER_EN);
                        buyer.setCustomerGroupName(LOWES_NAME_OF_CUSTOMER_GROUP);
                    });
        }

        // SGSMart直接赋值
        if (refSystem == RefSystemIdEnum.SGSMart || refSystem == RefSystemIdEnum.F21
                || refSystem == RefSystemIdEnum.JO_ANN || refSystem == RefSystemIdEnum.Target
                || refSystem == RefSystemIdEnum.BigLots || refSystem == RefSystemIdEnum.Walmart || refSystem ==RefSystemIdEnum.Walmart_Group
                || refSystem == RefSystemIdEnum.Veyer || refSystem == RefSystemIdEnum.Amazon || refSystem == RefSystemIdEnum.DollarTree || refSystem == RefSystemIdEnum.LOWES || refSystem == RefSystemIdEnum.TARGET_INSPECTORIO) {
            if (orderRsp.getBuyer() != null) {
                CustomerInfo detailBuyer = orderDetail.getBuyer();
                CustomerInfo customeBuyer = new CustomerInfo();
                CustomerRsp buyer = orderRsp.getBuyer();
                BeanUtils.copyProperties(buyer, customeBuyer);
                customeBuyer.setBuyerGroupName(buyer.getCustomerGroupName());
                customeBuyer.setBuyerGroup(buyer.getCustomerGroupCode());
                customeBuyer.setCustomerUsage(CustomerType.Buyer.getCode());
                customeBuyer.setBossNumber(buyer.getNumber() == null ? null : buyer.getNumber().longValue());
                customeBuyer.setCustomerGroupId(buyer.getCustomerGroupId());
                customeBuyer.setCustomerAddressCN(buyer.getAddressCn());
                customeBuyer.setCustomerAddressEN(buyer.getAddressEn());
                customeBuyer.setCustomerNameCN(buyer.getCustomerNameCn());
                customeBuyer.setCustomerNameEN(buyer.getCustomerNameEn());
                customeBuyer.setContactPersonEmail(buyer.getEmail());
                customeBuyer.setContactPersonName(buyer.getContactName());
                customeBuyer.setContactPersonPhone1(buyer.getTelephone());
                if (Func.isNotEmpty(detailBuyer)) {
                    if (Func.isBlank(customeBuyer.getCustomerId())) {
                        customeBuyer.setCustomerId(detailBuyer.getCustomerId());
                    }
                    if (Func.isBlank(customeBuyer.getCustomerNameCN())) {
                        customeBuyer.setCustomerNameCN(detailBuyer.getCustomerNameCN());
                    }
                    if (Func.isBlank(customeBuyer.getCustomerNameEN())) {
                        customeBuyer.setCustomerNameEN(detailBuyer.getCustomerNameEN());
                    }
                    if (Func.isBlank(customeBuyer.getCustomerGroupId())) {
                        customeBuyer.setCustomerGroupId(detailBuyer.getCustomerGroupId());
                    }
                    if (Func.isEmpty(customeBuyer.getAccountID()) || customeBuyer.getAccountID() <= 0) {
                        customeBuyer.setAccountID(detailBuyer.getAccountID());
                    }
                }
                orderDetail.setBuyer(customeBuyer);
            }
            if (orderRsp.getApplicant() != null) {
                CustomerInfo customerApplicant = new CustomerInfo();
                CustomerRsp applicant = orderRsp.getApplicant();
                BeanUtils.copyProperties(applicant, customerApplicant);
                customerApplicant.setCustomerUsage(CustomerType.Applicant.getCode());
                customerApplicant.setBossNumber(applicant.getNumber() == null ? null : applicant.getNumber().longValue());
                customerApplicant.setBuyerGroup(applicant.getCustomerGroupCode());
                customerApplicant.setBuyerGroupName(applicant.getCustomerGroupName());
                customerApplicant.setCustomerNameCN(applicant.getCustomerNameCn());
                customerApplicant.setCustomerNameEN(applicant.getCustomerNameEn());
                customerApplicant.setCustomerAddressCN(applicant.getAddressCn());
                customerApplicant.setCustomerAddressEN(applicant.getAddressEn());
                customerApplicant.setContactPersonName(applicant.getContactName());
                customerApplicant.setContactPersonPhone1(applicant.getTelephone());
                customerApplicant.setContactPersonEmail(applicant.getEmail());
                orderDetail.setApplicant(customerApplicant);
            }
            if (orderRsp.getPayer() != null) {
                CustomerInfo customerPayer = new CustomerInfo();
                CustomerRsp payer = orderRsp.getPayer();
                BeanUtils.copyProperties(payer, customerPayer);
                customerPayer.setCustomerUsage(CustomerType.Payer.getCode());
                customerPayer.setBossNumber(payer.getNumber() == null ? null : payer.getNumber().longValue());
                customerPayer.setBuyerGroup(payer.getCustomerGroupCode());
                customerPayer.setBuyerGroupName(payer.getCustomerGroupName());
                customerPayer.setCustomerNameCN(payer.getCustomerNameCn());
                customerPayer.setCustomerNameEN(payer.getCustomerNameEn());
                customerPayer.setCustomerAddressCN(payer.getAddressCn());
                customerPayer.setCustomerAddressEN(payer.getAddressEn());
                customerPayer.setContactPersonName(payer.getContactName());
                customerPayer.setContactPersonPhone1(payer.getTelephone());
                customerPayer.setContactPersonEmail(payer.getEmail());
                orderDetail.setPayer(customerPayer);
            }
            if (orderRsp.getAgent() != null) {
                CustomerInfo customerAgent = new CustomerInfo();
                CustomerRsp agent = orderRsp.getAgent();
                BeanUtils.copyProperties(agent, customerAgent);
                customerAgent.setCustomerUsage(CustomerType.Agent.getCode());
                customerAgent.setBossNumber(agent.getNumber() == null ? null : agent.getNumber().longValue());
                customerAgent.setBuyerGroup(agent.getCustomerGroupCode());
                customerAgent.setBuyerGroupName(agent.getCustomerGroupName());
                customerAgent.setCustomerNameCN(agent.getCustomerNameCn());
                customerAgent.setCustomerNameEN(agent.getCustomerNameEn());
                customerAgent.setCustomerAddressCN(agent.getAddressCn());
                customerAgent.setCustomerAddressEN(agent.getAddressEn());
                customerAgent.setContactPersonName(agent.getContactName());
                customerAgent.setContactPersonPhone1(agent.getTelephone());
                customerAgent.setContactPersonEmail(agent.getEmail());
                orderDetail.setAgent(customerAgent);
            }

            if (orderRsp.getSupplier() != null) {
                CustomerInfo supplierCustomer = new CustomerInfo();
                CustomerRsp supplier = orderRsp.getSupplier();
                BeanUtils.copyProperties(supplier, supplierCustomer);
                supplierCustomer.setCustomerUsage(CustomerType.Supplier.getCode());
                supplierCustomer.setBossNumber(supplier.getNumber() == null ? null : supplier.getNumber().longValue());
                supplierCustomer.setBuyerGroup(supplier.getCustomerGroupCode());
                supplierCustomer.setBuyerGroupName(supplier.getCustomerGroupName());
                supplierCustomer.setCustomerNameCN(supplier.getCustomerNameCn());
                supplierCustomer.setCustomerNameEN(supplier.getCustomerNameEn());
                supplierCustomer.setCustomerAddressCN(supplier.getAddressCn());
                supplierCustomer.setCustomerAddressEN(supplier.getAddressEn());
                supplierCustomer.setContactPersonName(supplier.getContactName());
                supplierCustomer.setContactPersonPhone1(supplier.getTelephone());
                supplierCustomer.setContactPersonEmail(supplier.getEmail());
                orderDetail.setSupplier(supplierCustomer);
            }

            if (orderRsp.getManufacture() != null) {
                CustomerInfo manufactureCustomer = new CustomerInfo();
                CustomerRsp manufacture = orderRsp.getManufacture();
                BeanUtils.copyProperties(manufacture, manufactureCustomer);
                manufactureCustomer.setCustomerUsage(CustomerType.Manufacture.getCode());
                manufactureCustomer.setBossNumber(manufacture.getNumber() == null ? null : manufacture.getNumber().longValue());
                manufactureCustomer.setBuyerGroup(manufacture.getCustomerGroupCode());
                manufactureCustomer.setBuyerGroupName(manufacture.getCustomerGroupName());
                manufactureCustomer.setCustomerNameCN(manufacture.getCustomerNameCn());
                manufactureCustomer.setCustomerNameEN(manufacture.getCustomerNameEn());
                manufactureCustomer.setCustomerAddressCN(manufacture.getAddressCn());
                manufactureCustomer.setCustomerAddressEN(manufacture.getAddressEn());
                manufactureCustomer.setContactPersonName(manufacture.getContactName());
                manufactureCustomer.setContactPersonPhone1(manufacture.getTelephone());
                manufactureCustomer.setContactPersonEmail(manufacture.getEmail());
                orderDetail.setManufacture(manufactureCustomer);
            }
        }

    }

    /**
     * 设置 TestRequest
     *
     * @param orderDetailInfo
     * @param testRequestRsp
     */
    private void buildTestRequest(OrderDetailDto orderDetailInfo, TestRequestRsp testRequestRsp) {
        if (Func.isEmpty(testRequestRsp)) {
            return;
        }
        // 默认英文
        ReportLanguage reportLanguage = ReportLanguage.EnglishReportOnly;
        if (testRequestRsp != null && StringUtils.isNotBlank(testRequestRsp.getReportLanguage())) {
            ReportLanguage name = ReportLanguage.findName(testRequestRsp.getReportLanguage());
            if (name != null) {
                reportLanguage = name;
            }
        }
        TestRequestInfo testRequestInfo = new TestRequestInfo();
        BeanUtils.copyProperties(testRequestRsp, testRequestInfo);
        testRequestInfo.setReportLanguage(reportLanguage.getCode());

        orderDetailInfo.setTestRequest(testRequestInfo);
    }

    /**
     * 设置 Sample
     *
     * @param trfOrderRsp
     * @param orderDetail
     */
    private void buildDffSampleInfo(TrfOrderRsp trfOrderRsp, OrderDetailDto orderDetail, Map<String, List<String>> trfSampleNoMaps, String languageCode, RefSystemIdEnum refSystem,String productLineCode) {
        if (ObjectUtils.isEmpty(trfOrderRsp)) {
            return;
        }
        ReportLanguage reportLanguage = ReportLanguage.findName(languageCode);
        if (reportLanguage == null) {
            return;
        }

        // 获取需要生成的多语言
        List<LanguageType> languageType = LanguageType.getLanguageType(reportLanguage.getLanguageId());

        // 是否需要 ？？？
        if (CollectionUtils.isEmpty(orderDetail.getCareLabels())) {
            orderDetail.setCareLabels(Lists.newArrayList(this.createCareLabelInfo()));
        } else {
            orderDetail.setCareLabels(trfOrderRsp.getCareLabels());
        }

        List<ProductSampleRsp> productSampleRspList = Lists.newArrayList();

        // TODO 后续使用新结构 productSampleRspList, 现有的客户后续修改结构
        if ((RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.SGSMart, RefSystemIdEnum.SEMIR, RefSystemIdEnum.FastFish, RefSystemIdEnum.PeaceBird, RefSystemIdEnum.TIC, RefSystemIdEnum.Septwolves,
                RefSystemIdEnum.F21, RefSystemIdEnum.JO_ANN, RefSystemIdEnum.Walmart,RefSystemIdEnum.Walmart_Group, RefSystemIdEnum.Target, RefSystemIdEnum.BigLots, RefSystemIdEnum.DollarTree, RefSystemIdEnum.Veyer, RefSystemIdEnum.Amazon,
                RefSystemIdEnum.LOWES, RefSystemIdEnum.TARGET_INSPECTORIO
        )
                // gpo希音供应商使用新结构
                || (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.SheinSupplier) &&
                !StringUtils.equalsIgnoreCase(ProductLineType.SL.getProductLineAbbr(), ProductLineContextHolder.getProductLineCode()))
        )
                && !CollectionUtils.isEmpty(trfOrderRsp.getProductSampleRspList())) {
            for (ProductListRsp productListRsp : trfOrderRsp.getProductSampleRspList()) {
                ProductSampleRsp sampleRsp = new ProductSampleRsp();
                ProductInfo productRsp = new ProductInfo();
                BeanUtils.copyProperties(productListRsp, sampleRsp);
                // product Copy
                BeanUtils.copyProperties(productListRsp.getProduct(), productRsp);
                productRsp.setLanguageID(productListRsp.getLanguageId());
                productRsp.setdFFFormID(productListRsp.getProduct().getDffFormId());
                productRsp.setpONo(productListRsp.getProduct().getPoNo());
                productRsp.setId("");

                List<ProductSampleInfo> productSamples = Lists.newArrayList();
                if (!CollectionUtils.isEmpty(productListRsp.getProductSamples())) {
                    int itemNo = 1;
                    for (TrfProductSampleRsp productSampleRsp : productListRsp.getProductSamples()) {
                        ProductSampleInfo productSampleInfo = new ProductSampleInfo();

                        BeanUtils.copyProperties(productSampleRsp, productSampleInfo);
//                        productSampleInfo.setNoOfSample(NumberUtil.toInt(productSampleRsp.getNoOfSample()));
                        productSampleInfo.setId(null);
                        productSampleInfo.setHeaderID(null);
                        productSampleInfo.setExternalSampleNo(productSampleRsp.getSampleNo());
                        productSampleInfo.setLanguageID(productListRsp.getLanguageId());
//                        productSampleInfo.setLanguageCode(language.getCode());
                        productSampleInfo.setdFFFormID(StringUtils.defaultString(productSampleRsp.getdFFFormID(), productSampleRsp.getDffFormId()));
                        if (StringUtils.isBlank(productSampleRsp.getProductItemNo())) {
                            productSampleInfo.setProductItemNo(String.format("SupplierCode_%s", itemNo));
                        }
                        setSampleId(trfSampleNoMaps, refSystem, productSampleRsp, itemNo, productSampleInfo, productLineCode);
                        if (StringUtils.isBlank(productSampleRsp.getNoOfSample())) {
                            productSampleInfo.setNoOfSample(NumberUtil.toInt(productSampleRsp.getNoOfSample()) > 0 ? NumberUtil.toInt(productSampleRsp.getNoOfSample()) : 1);//itemNo
                        } else {
                            productSampleInfo.setNoOfSample(NumberUtil.toInt(productSampleRsp.getNoOfSample()));
                        }
                        productSampleInfo.setpONo(StrUtil.blankToDefault(productSampleRsp.getPoNo(), productSampleRsp.getpONo()));
                        itemNo++;
                        productSampleInfo.setReferenceNo(productSampleRsp.getReferenceNo());

                        if (!CollectionUtils.isEmpty(productSampleRsp.getComponentSamples())) {
                            List<ComponentSamplesRsp> componentSamples = Lists.newArrayList();
                            for (TrfComponentSampleRsp componentInfo : productSampleRsp.getComponentSamples()) {
                                ComponentSamplesRsp componentSamplesRsp = new ComponentSamplesRsp();
                                BeanUtils.copyProperties(componentInfo, componentSamplesRsp);

                                componentSamples.add(componentSamplesRsp);
                            }
                            productSampleInfo.setComponentSamples(componentSamples);
                        }
                        productSamples.add(productSampleInfo);
                    }
                }
                sampleRsp.setLanguageID(productListRsp.getLanguageId());
                sampleRsp.setProduct(productRsp);
                sampleRsp.setProductSamples(productSamples);

                if (!CollectionUtils.isEmpty(productListRsp.getCareLabels())) {
                    List<CareLabelInfo> careLabels = productListRsp.getCareLabels();
                    for (CareLabelInfo careLabel : careLabels) {
                        careLabel.setId(null);
                    }
                }else if(!CollectionUtils.isEmpty(trfOrderRsp.getCareLabels())){
                    //SCI-1381
                    List<CareLabelInfo> deepCopy = JSON.parseArray(JSON.toJSONString(trfOrderRsp.getCareLabels()), CareLabelInfo.class);
                    sampleRsp.setCareLabels(deepCopy);
                }else {
                    sampleRsp.setCareLabels(orderDetail.getCareLabels());
                }
                //新增加判断
                removeCareLableId(sampleRsp);

                productSampleRspList.add(sampleRsp);
            }
            orderDetail.setProductSampleRspList(productSampleRspList);
            // 设置外层 productSamples product
            orderDetail.setProduct(productSampleRspList.get(0).getProduct());
            orderDetail.setProductSamples(productSampleRspList.get(0).getProductSamples());

            if (RefSystemIdEnum.check(RefSystemIdAdapter.map.get(refSystem.getRefSystemId()), RefSystemIdEnum.SGSMart)) {
                this.dealCareLabelInfo(productSampleRspList.get(0).getCareLabels(), orderDetail);
            }
            return;
        }

        for (LanguageType language : languageType) {

            ProductSampleRsp sampleRsp = new ProductSampleRsp();
            sampleRsp.setLanguageCode(language.getCode());
            sampleRsp.setLanguageID(language.getLanguageId());
            sampleRsp.setCareLabels(orderDetail.getCareLabels());

            // 设置 product
            ProductInfo productInfo = new ProductInfo();
            if (trfOrderRsp.getProduct() != null) {
                TrfProductRsp product = trfOrderRsp.getProduct();
                BeanUtils.copyProperties(product, productInfo);

                productInfo.setdFFFormID(product.getDffFormId());
                productInfo.setLanguageID(language.getLanguageId());
                productInfo.setLanguageCode(language.getCode());
                productInfo.setpONo(product.getPoNo());
            }
            sampleRsp.setProduct(productInfo);

            List<ProductSampleInfo> productSamples = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(trfOrderRsp.getProductSamples())) {
                int itemNo = 1;
                for (TrfProductSampleRsp trfProductSampleItem : trfOrderRsp.getProductSamples()) {
                    ProductSampleInfo productSampleInfo = new ProductSampleInfo();
                    BeanUtils.copyProperties(trfProductSampleItem, productSampleInfo);

                    List<String> sampleNos = trfSampleNoMaps.get(trfProductSampleItem.getReferenceNo());
                    productSampleInfo.setLanguageID(language.getLanguageId());
                    productSampleInfo.setLanguageCode(language.getCode());
                    productSampleInfo.setdFFFormID(trfProductSampleItem.getDffFormId());
                    productSampleInfo.setProductItemNo(String.format("SupplierCode_%s", itemNo));
                    if (trfProductSampleItem.getSample_hide() == null || StringUtils.isBlank(trfProductSampleItem.getSample_hide())) {
                        // 防止 mapping 数据缺少
                        productSampleInfo.setSampleID(CollectionUtils.isEmpty(sampleNos) ? "A" : sampleNos.get(0));
                    } else {
                        productSampleInfo.setSampleID(trfProductSampleItem.getSample_hide());
                    }
                    productSampleInfo.setNoOfSample(NumberUtil.toInt(trfProductSampleItem.getNoOfSample()) > 0 ? NumberUtil.toInt(trfProductSampleItem.getNoOfSample()) : 1);//itemNo
                    productSampleInfo.setpONo(trfProductSampleItem.getPoNo());
                    itemNo++;
                    productSampleInfo.setReferenceNo(trfProductSampleItem.getReferenceNo());

                    if (!CollectionUtils.isEmpty(trfProductSampleItem.getComponentSamples())) {
                        List<ComponentSamplesRsp> componentSamples = Lists.newArrayList();
                        for (TrfComponentSampleRsp componentInfo : trfProductSampleItem.getComponentSamples()) {
                            ComponentSamplesRsp componentSamplesRsp = new ComponentSamplesRsp();
                            BeanUtils.copyProperties(componentInfo, componentSamplesRsp);

                            componentSamples.add(componentSamplesRsp);
                        }
                        productSampleInfo.setComponentSamples(componentSamples);
                    }
                    productSamples.add(productSampleInfo);
                }
            }
            sampleRsp.setProductSamples(productSamples);

            productSampleRspList.add(sampleRsp);
        }

        orderDetail.setProductSampleRspList(productSampleRspList);
        // 设置外层 productSamples product
        orderDetail.setProduct(productSampleRspList.get(0).getProduct());
        orderDetail.setProductSamples(productSampleRspList.get(0).getProductSamples());
    }

    private static void setSampleId(Map<String, List<String>> trfSampleNoMaps, RefSystemIdEnum refSystem, TrfProductSampleRsp productSampleRsp, int itemNo, ProductSampleInfo productSampleInfo, String productLineCode) {
        if (StringUtils.isBlank(productSampleRsp.getSampleID())) {
            // SCI-1423 7个cp客户，如果是SL自动分配，如果是HL返回空
            if(RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.F21, RefSystemIdEnum.JO_ANN,
                    RefSystemIdEnum.Walmart,RefSystemIdEnum.Walmart_Group, RefSystemIdEnum.Target, RefSystemIdEnum.BigLots, RefSystemIdEnum.DollarTree, RefSystemIdEnum.Veyer)) {
                if(Objects.equals(productLineCode, ProductLineTypeEnum.SL.getProductLineAbbr())) {
                    String sampleId = NumberUtil.numberToLetter(itemNo);
                    productSampleInfo.setSampleID(sampleId);
                }
                return;
            }
            List<String> sampleNos = trfSampleNoMaps.get(productSampleRsp.getReferenceNo());
            if (StringUtils.isBlank(productSampleRsp.getSample_hide()) && !CollectionUtils.isEmpty(sampleNos)) {
                if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.TIC)) {
                    //
                    String sampleId = NumberUtil.numberToLetter(itemNo);
                    productSampleInfo.setSampleID(sampleId);
                    //productSampleInfo.setSampleID(sampleNos.get(itemNo - 1));
                } else {
                    productSampleInfo.setSampleID(sampleNos.get(0));
                }
            } else {
                productSampleInfo.setSampleID(productSampleRsp.getSample_hide());
            }
        }
        if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.SGSMart)) {
            productSampleInfo.setSampleID(productSampleRsp.getSampleID());
        }
    }

    private static void removeCareLableId(ProductSampleRsp sampleRsp) {
        List<CareLabelInfo> careLabels = sampleRsp.getCareLabels();
        if(!CollectionUtils.isEmpty(careLabels)) {
            for (CareLabelInfo careLabel : careLabels) {
                careLabel.setId(null);
            }
        }
    }

    /**
     * 设置TestLine
     *
     * @param trfOrderRsp
     * @param orderDetail
     */
    private void buildTestLineInfo(TrfOrderRsp trfOrderRsp, OrderDetailDto orderDetail) {

        if (CollectionUtils.isEmpty(trfOrderRsp.getTestLines())) {
            return;
        }
        List<TrfTestLineRsp> trfTestLines = trfOrderRsp.getTestLines();

        List<SaveTestLineInfo> testLines = Lists.newArrayList();
        for (TrfTestLineRsp trfTestLine : trfTestLines) {
            SaveTestLineInfo saveTestLineInfo = new SaveTestLineInfo();
            if (NumberUtil.toInt(trfTestLine.getTestLineId()) == 0) {
                continue;
            }
            BeanUtils.copyProperties(trfTestLine, saveTestLineInfo);
            testLines.add(saveTestLineInfo);
        }
        orderDetail.setTestLines(testLines);

    }


    /**
     * 设置TestLine
     *
     * @param trfOrderRsp
     * @param orderDetail
     */
    private void buildAttachment(TrfOrderRsp trfOrderRsp, OrderDetailDto orderDetail) {

        if (CollectionUtils.isEmpty(trfOrderRsp.getOrderAttachmentDTOS())) {
            return;
        }
        List<OrderAttachmentRsp> attachmentRspList = trfOrderRsp.getOrderAttachmentDTOS();
        List<OrderAttachmentInfo> orderAttachmentList = attachmentRspList.parallelStream().map(attachmentRsp -> {
            OrderAttachmentInfo attachmentInfo = new OrderAttachmentInfo();
            attachmentInfo.setAttachmentName(attachmentRsp.getAttachmentName());
            attachmentInfo.setSize(attachmentRsp.getSize());
            attachmentInfo.setCloudID(attachmentRsp.getCloudID());
            attachmentInfo.setFileID(attachmentRsp.getFileId());
            return attachmentInfo;
        }).collect(Collectors.toList());

        orderDetail.setOrderAttachmentDTOS(orderAttachmentList);

    }

    /**
     * 设置图片
     *
     * @param reqObject
     * @param orderDetail
     */
    private void buildPicture(GetRefNoReq reqObject, OrderDetailDto orderDetail) {
        if (CollectionUtils.isEmpty(reqObject.getTrfInfos())) {
            return;
        }
        List<String> pictureIds = Lists.newArrayList();
        for (TrfInfoReq trfInfoReq : reqObject.getTrfInfos()) {
            if (CollectionUtils.isEmpty(trfInfoReq.getPictureIds())) {
                continue;
            }

            pictureIds.addAll(trfInfoReq.getPictureIds());
        }
        orderDetail.setPictureIds(pictureIds);
    }

    /**
     * 补充信息
     *
     * @param orderDetail
     */
    private void buildDetailSupplement(TrfOrderRsp trfOrderRsp, OrderDetailDto orderDetail, Map<String, String> trfObjectNoMap, Integer refSystemId) {

        if (CollectionUtils.isEmpty(trfOrderRsp.getProductSamples())) {
            return;
        }
        List<TrfProductSampleRsp> trfProductSamples = trfOrderRsp.getProductSamples();
        Map<String, String> trfCreateTypeMap = trfProductSamples.stream().filter(item -> !StringUtils.isBlank(item.getExtObjectId()))
                .collect(Collectors.toMap(TrfProductSampleRsp::getReferenceNo, TrfProductSampleRsp::getCreateType, (k1, k2) -> k2));

        OrderHeaderDTO headers = orderDetail.getHeaders();
        List<ReferenceInfo> references = Lists.newArrayList();

        List<ProductSampleInfo> productSamples = orderDetail.getProductSamples();
        Map<String, String> trfExtObjectMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(productSamples)) {
            trfExtObjectMap = productSamples.stream()
                    .filter(item -> !StringUtils.isBlank(item.getExtObjectId()))
                    .collect(Collectors.toMap(ProductSampleInfo::getReferenceNo, ProductSampleInfo::getExtObjectId, (k1, k2) -> k2));
        }

        for (String trfNo : trfCreateTypeMap.keySet()) {
            String createType = trfCreateTypeMap.get(trfNo);
            ReferenceInfo referenceInfo = new ReferenceInfo();
            referenceInfo.setReferenceNo(trfNo);
            referenceInfo.setCreateType(NumberUtil.toInt(createType));
            referenceInfo.setPackageBarcode(trfObjectNoMap.get(trfNo));
            referenceInfo.setExtObjectId(trfExtObjectMap.get(trfNo));
            references.add(referenceInfo);
        }
        headers.setReferences(references);

        headers.setReferenceId(refSystemId);
        headers.setReferenceNo(StringUtil.join(Lists.newArrayList(trfCreateTypeMap.keySet()), ","));

    }

    /**
     * 构建Customer
     *
     * @param locationCode
     * @param customerType
     * @param orderRsp
     * @return
     */
    private CustomerInfo getNewCustomer(String locationCode, CustomerType customerType, CustomerRsp orderRsp) {
        CustomerListQueryReq customerReq = new CustomerListQueryReq();
        if (Func.isNotEmpty(orderRsp.getAccountId()) && orderRsp.getAccountId() > 0) {
            customerReq.setAccountIDList(Sets.newHashSet(NumberUtil.toLong(orderRsp.getAccountId())));
        }
        customerReq.setCustomerContactIdList(Sets.newHashSet(orderRsp.getCustomerContactId()));
        customerReq.setBuCode(ProductLineContextHolder.getProductLineCode());
        customerReq.setLocationCode(locationCode);
        switch (customerType) {
            case Applicant:
                customerReq.setContactRole("SHIP_TO");
                customerReq.setCode("SHIP_TO");
                break;
            case Payer:
                customerReq.setContactRole("BILL_TO");
                customerReq.setCode("BILL_TO");
                break;
            case Buyer:
            case Agent:
                customerReq.setDisplayType("account");
                break;
        }
        customerReq.setNumber(orderRsp.getNumber());
        List<ContactAddressPO> contactAddress = customerClient.queryCustomerList(customerReq);
        if (contactAddress == null || contactAddress.isEmpty()) {
            return null;
        }

        ContactAddressPO contactAddressDTO = contactAddress.stream().filter(item -> NumberUtil.equals(NumberUtil.toLong(item.getNumber()), orderRsp.getNumber())).findFirst().orElse(null);
        if (contactAddressDTO == null) {
            return null;
        }

        CustomerInfo customer = new CustomerInfo();
        BeanUtils.copyProperties(contactAddressDTO, customer);
        customer.setCustomerId(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerId()));
        customer.setCustomerGroupId(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerGroupId()));
        customer.setCustomerAddressCN(StringUtil.isNullOrEmpty(contactAddressDTO.getAddressCn()));
        customer.setCustomerAddressEN(StringUtil.isNullOrEmpty(contactAddressDTO.getAddressEn()));
        customer.setCustomerNameCN(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerNameCn()));
        customer.setCustomerNameEN(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerNameEn()));
        customer.setContactPersonEmail(StringUtil.isNullOrEmpty(contactAddressDTO.getEmail()));
        customer.setContactPersonFax(StringUtil.isNullOrEmpty(contactAddressDTO.getFax()));
        customer.setContactPersonPhone1(StringUtil.isNullOrEmpty(contactAddressDTO.getTelephone()));
        customer.setContactPersonPhone2(StringUtil.isNullOrEmpty(contactAddressDTO.getMobile()));
        customer.setContactPersonName(StringUtil.isNullOrEmpty(contactAddressDTO.getContactName()));
        customer.setCustomerUsage(customerType.getCode());
        customer.setBossSiteUseID(contactAddressDTO.getBossSiteUseID());
        customer.setBossContactID(contactAddressDTO.getBossContactID());
        customer.setPrimaryFlag(contactAddressDTO.getPrimaryFlag());
        customer.setAccountID((Func.isEmpty(orderRsp.getAccountId()) || Objects.equals(orderRsp.getAccountId(), 0)) ? contactAddressDTO.getAccountID() : NumberUtil.toLong(orderRsp.getAccountId()));
        customer.setBossNumber(contactAddressDTO.getNumber() == null ? 0L : Long.parseLong(contactAddressDTO.getNumber()));
        customer.setContactAddressID(contactAddressDTO.getCustomerContactId());
        customer.setBuyerGroup(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerGroupCode()));
        customer.setBuyerGroupName(StringUtil.isNullOrEmpty(contactAddressDTO.getCustomerGroupName()));
        customer.setBossLocationCode(contactAddressDTO.getBossLocationCode());
        customer.setMonthlyPayment(StringUtils.equals(contactAddressDTO.getMonthlyPayment(), "0") ? "N" : "Y");
        customer.setOrganizationName(contactAddressDTO.getOrganizationName());
        customer.setPaymentTermName(contactAddressDTO.getPaymentTermName());

        return customer;
    }

    public static Map<String, Object> objectToMap(Object object) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(object));
        }
        return map;
    }

    /**
     * 构建 CareLabel
     *
     * @return
     */
    private CareLabelInfo createCareLabelInfo() {
        CareLabelInfo careLabelInfo = new CareLabelInfo();
        careLabelInfo.setProductItemNo(new ArrayList<>());
        careLabelInfo.setCareLabelSeq(1);
        careLabelInfo.setImgArray(new ArrayList<>());
        return careLabelInfo;
    }

    /**
     * 构建 CareLabel
     *
     * @return
     */
    private void dealCareLabelInfo(List<CareLabelInfo> careLabels, OrderDetailDto orderDetail) {
        String orderId = orderDetail.getHeaders().getNewOrderId();
        //多语言的洗唛，只需要其中一个语言的图标进行copy就行
        Map<String, FileInfo> oldFileIdNewFileInfo = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(careLabels)) {
            //获取有洗唛的对象 执行洗唛图标copy
            for (CareLabelInfo careLabel : careLabels) {
                String careLabelFileId = careLabel.getCareLabelFileId();
                if (StringUtils.isBlank(careLabelFileId)) {
                    continue;
                }
                //需要copy一份洗唛图标，用作preorder
                FileInfo fileInfo = new FileInfo();
                fileInfo.setId(careLabelFileId);
                fileInfo.setOrderId(orderId);
                fileInfo.setObjectID(orderId);
                CustomResult customResult = fileClient.copyFile(fileInfo);
                boolean success = customResult.isSuccess();
                if (!success) {
                    logger.error("Copy CareLabel file fail!");
                    continue;
                }
                FileInfo info = (FileInfo) customResult.getData();
                oldFileIdNewFileInfo.put(careLabelFileId, info);
            }
        }
        if (!oldFileIdNewFileInfo.isEmpty()) {
            outer:
            for (ProductSampleRsp productSampleRsp : orderDetail.getProductSampleRspList()) {
                List<CareLabelInfo> careLabelsInfos = productSampleRsp.getCareLabels();
                inner:
                for (CareLabelInfo careLabelsInfo : careLabelsInfos) {
                    String careLabelFileId = careLabelsInfo.getCareLabelFileId();
                    if (StringUtils.isBlank(careLabelFileId)) {
                        continue inner;
                    }
                    FileInfo fileInfo = oldFileIdNewFileInfo.get(careLabelFileId);
                    careLabelsInfo.setCareLabelFileId(fileInfo.getId());
                    careLabelsInfo.setCloudId(fileInfo.getCloudID());
                }
            }
        }
//        CareLabelInfo careLabelInfo = new CareLabelInfo();
//        careLabelInfo.setProductItemNo(new ArrayList<>());
//        careLabelInfo.setCareLabelSeq(1);
//        careLabelInfo.setImgArray(new ArrayList<>());
    }

    /**
     * // TODO SL SGSMart
     *
     * @param reqObject
     * @return
     */
    public CustomResult getImportTrfOrderInfo(OrderImportTrfReq reqObject) {
        CustomResult rspResult = new CustomResult();


        if (reqObject == null || NumberUtil.toInt(reqObject.getRefSystemId()) == 0 || StringUtils.isBlank(reqObject.getTrfNo())) {
            return rspResult.fail("请检查参数refSystemId、trfNo");
        }

        // TODO 调用  SCI import 方法
        TrfImportReq trfImportReq = new TrfImportReq();
        trfImportReq.setTrfNo(reqObject.getTrfNo());
        trfImportReq.setRefSystemId(reqObject.getRefSystemId());
        //数据查询
        TrfImportResult importRes = sciTrfBizService.importTrf(trfImportReq);
        if (importRes == null) {
            return rspResult;
        }
        // TODO 查询 源数据
        List<CustomerTrfInfoRsp> customerTrfInfoList = trfService.getCustomerTrfInfoList(Lists.newArrayList(reqObject.getTrfNo()), null);
        rspResult.setSuccess(CollectionUtils.isEmpty(customerTrfInfoList));
        rspResult.setData(CollectionUtils.isEmpty(customerTrfInfoList) ? null : customerTrfInfoList.get(0));
        return rspResult;
    }

    private Pair resolveIlayerAction(SysHeaderDto sysHeaderDto) {
        Map<String, Pair> actionMap = new HashMap<>();
        actionMap.put("1", Pair.of("GetSubcontractDetail", "GetSubcontractDetail"));
        actionMap.put("2", Pair.of("GetReportCoverPage", "GetReportCoverPage"));
        return actionMap.get(sysHeaderDto.getAction());
    }


}
