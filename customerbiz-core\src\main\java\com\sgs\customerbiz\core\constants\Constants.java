package com.sgs.customerbiz.core.constants;

public final class Constants {
    /**
     * 日志前缀
     */
    public static final String LOG_PREFIX = "logPrefix";
    /**
     * SGS Token Key
     */
    public static final String SGS_TOKEN = "sgsToken";

    public static final String SupplierCode = "SupplierCode_";

    /**
     * 业务字段
     */
    public static final String PRODUCT_LINE_CODE = "productLineCode";
    public static final String CUSTOMER_GROUP_CODE = "customerGroupCode";

    public static final String DFF_FORM_GROUP_ID = "dffFormGroupId";
    public static final String GRID_FORM_GROUP_ID = "gridFormGroupId";
    public static final String DFF_FORM_ID = "dffFormId";
    public static final String GRID_FORM_ID = "gridFormId";
    public static final String REF_SYSTEM_ID = "refSystemId";

    public static final String USER_DEFAULT = "system";
    public static final String BU_CODE = "buCode";
    public static final String LOCATION_CODE = "locationCode";
    public static final String LAB_CODE = "$.lab.labCode";
    public static final String HEADERS_LAB_CODE = "$.headers.lab.labCode";
    public static final String LANGUAGE_ID = "languageId";
    public static final String ACTION = "action";

    public static final String SGS_TRF_NO = "sgsTrfNo";
    public static final String ORDER_NO = "orderNo";
    public static final String ORDER_NO_TIC = "orderNoTic";
    public static final String PACKAGE_BARCODE = "packageBarcode";
    public static final String CREATED_DATE = "createdDate";
    public static final String CREATED_BY = "createdBy";
    public static final String DETAIL = "detail";
    public static final String PICTURE_ID = "pictureId";
    public static final String PICTURE_IDS = "pictureIds";
    public static final String PICTURE_URL_LIST = "pictureUrlList";
    public static final String PICTURES = "pictures";
    public static final String SAMPLE_FORM = "sampleForm";
    public static final String PDF_URL = "PdfUrl";
    public static final String SAMPLE_NO = "sampleNo";
    public static final String ID = "id";
    public static final String TRF_ID = "trfId";
    public static final String TRF_NO = "trfNo";
    public static final String TRF_STATUS = "trfStatus";
    public static final String STATUS = "status";
    public static final String TRF_STATE = "trfState";
    public static final String CHILD_ORDER_LIST = "childOrderList";

    public static final String ORDER_ID = "orderId";

    // 希音
    public static final String BATCH_NO = "batchNo";
    public static final String LAB_TYPE = "labType";
    public static final String SKC = "skc";
    public static final String CREATE_TYPE = "createType";
    public static final String GOODS_PICTURE_URL = "goodsPictureUrl";
    public static final String GOODS_COLOR = "goodsColor";
    public static final String GOODS_PICTURE_URL_LIST = "goodsPictureUrlList";
    public static final String CHECK_ITEM_LIST = "checkItemList";
    public static final String IMG_URL_LIST = "imgUrlList";
    public static final String IMG_URL = "imgUrl";
    public static final String CHECK_TYPE = "checkType";
    public static final String ITEM_CODE = "itemCode";
    public static final String MATERIAL_DETAIL_LIST = "materialDetailList";
    public static final String MATERIAL_NAME = "materialName";
    public static final String MATERIAL_SKU = "materialSku";
    public static final String MATERIAL_PIC = "materialPic";
    public static final String FLOWER_PIC_URL = "flowerPicUrl";

    // anta
    public static final String ANTA_TEST_PRO = "checkItemList";

    // 李宁
    public static final String ORDER_NO_TRF = "ORDER_NO";


    // SGSMart
    public static final String GET_SGS_MART_TRF_INFO = "GetSGSMartTrfInfo";
    public static final String HEADERS = "headers";
    public static final String TRF_SUBMISSION_DATE = "trfSubmissionDate";
    public static final String TRF_ATTACHMENTS = "trfAttachments";
    public static final String ATTACHMENT_URL = "attachmentUrl";
    public static final String ATTACHMENT_URL_HIDE = "attachmentUrl_hide";
    public static final String DOWNLOAD_FILE_HIDE = "downloadFile_hide";
    public static final String CLOUD_ID = "cloudId";

    public static final String ITEM_DTS = "itemDts";
    public static final String ITEM_NAME = "itemName";

    public static final String SYNC_GPO_ORDER_INFO_TO_SGSMART_ACTION = "SyncGPOOrderInfoToSGSMart";
    public static final String SYNC_SCI_ORDER_INFO_TO_SGSMART_ACTION = "SyncSCIOrderInfoToSGSMart";

    // TIC
    public static final String SAMPLEFORM = "sampleForm";
    public static final String ATTACHMENTS = "attachments";
    public static final String FILE_ID = "fileId";
    public static final String FILE_NAME = "fileName";

    /**
     * 配置
     */
    public static final String SCENE_TYPES = "sceneTypes";
    public static final String SEARCH_TYPE = "searchType";
    public static final String SCENE_TODO_LIST = "trf_todo_list_list";
    public static final String SCENE_BOUND_LIST = "trf_bound_list";
    public static final String SCENE_EXPORT_LIST = "trf_todo_list_export";
    public static final String SCENE_BOUND_EXPORT = "trf_bound_export";
    public static final String CREATE_ORDERBY_TRF = "CreateOrderByTRF";

    public static final String CREATE_MORE_ORDER = "createMoreOrder";
    public static final String MODE = "mode";
    public static final String REMOTE = "REMOTE";

    public static final String FIELD_CODE = "fieldCode";
    public static final String FIELD_SEQ = "fieldSeq";
    public static final String OPTIONS = "options";
    public static final String TRANS = "trans";
    public static final String WIDTH = "width";
    public static final String RULES = "rules";
    public static final String DISPLAY_NAME = "displayName";
    public static final String PAGE = "page";
    public static final String ROWS = "rows";

    public static final String CANCELLED_BY = "cancelledBy";
    public static final String CANCELLED_DATE = "cancelledDate";
    public static final String CANCELLED_REASON = "cancelledReason";
    public static final String CANCELLED_REASON_CONTENT = "cancelledReasonContent";
    public static final String EnquiryNo = "enquiryNo";

    /**
     * 前端用 配置字段
     */
    public static final String SHOW_PIC_HIDE = "showPic_hide";
    public static final String ID_HIDE = "id_hide";
    public static final String LIST_ROW_ID_HIDE = "list_row_id_hide";
    public static final String GOODS_PICTURE = "Goods Picture";
    public static final String GENERAL_ORDER_ID = "generalOrderId_hide";
    public static final String GROUP_ID_HIDE = "groupId_hide";
    public static final String TRF_NO_HIDE = "trfNo_hide";
    public static final String ID_CHECKBOX = "id_checkbox";
    public static final String SAMPLE_HIDE = "sample_hide";
    public static final String TRF_REPORT_LEVEL_HIDE = "trfReportLevel_hide";
    public static final String SAMPLE_DISABLE = "sample_disable";
    public static final String CHECKBOX_HIDE = "checkbox_hide";
    public static final String BUSINESS_LICENSE_NAME = "$.businessLicenseName";


    public static final long CREATE_SGSMART_TRF_BY_CUSTOMER_TRF = 28L;
    /**
     * 固定字段
     */
    public static final Long SHEIN_Customer_Boss_No = Long.valueOf(4334450);
    public static final Long SHEIN_Customer_Account_Id = Long.valueOf(********);


    /**
     * 请求URL接口
     */

    /**
     * 获取希音信息url
     */
    public static final String GET_SHEIN_TRF_INFO_URL = "/openapi/sync/getSheinTrfInfo";

    /**
     * 获取testLine mapping 关系
     */
    public static final String QUERY_TEST_LINE_MAPPING = "/openapi/sync/queryTestLineMapping";

    /**
     * 获取 trf 信息
     */
    public static final String GET_ILayer_TRF_INFO_URL = "/openapi/sync/getInfo";
    /**
     * 获取模块的 UI 展示
     */
    public static final String GET_TRF_UI_SETTING = "/openapi/Sync/getTRFUISetting";
    /**
     * 获取 模块 详细数据
     */
    public static final String FUNC_CONVERT_DATA = "/openapi/func/convertData";

    public static final String API_URL_RD_IMPORT_DATA = "/api/report-data/import";

    public static final String API_URL_RD_BATCH_IMPORT_DATA = "/api/report-data/batch-import";

    public static final String API_URL_RD_BATCH_EXPORT_DATA = "/api/report-data/batch-export";
    public static final String API_URL_RD_BATCH_EXPORT_HEADER = "/api/report-data/batch-export-header";
    public static final String API_URL_RD_BATCH_EXPORT_DATA_BY_VERSION = "/api/report-data/exportByVersion";
    public static final String API_URL_RD_BATCH_EXPORT_DATA_ALL_STATUS = "/api/report-data/batch-export-all-status";
//    public static final String API_URL_RD_EXPORT_DATA_BY_TRF_NO = "/api/report-data/batch-export-by-trf-no";

    public static final String API_URL_RD_EXIST_REPORT_DATA = "/api/report-data/exist";

    public static final String API_URL_RD_BATCH_EXIST_REPORT_DATA = "/api/report-data/batch-exist";

    public static final String API_URL_RD_IMPORT_QUOTATION = "/api/report-data/import-quotation";

    public static final String API_URL_RD_EXPORT_QUOTATION = "/api/report-data/export-quotation";

    public static final String API_URL_RD_IMPORT_INVOICE = "/api/report-data/import-invoice";

    public static final String API_URL_RD_EXPORT_INVOICE = "/api/report-data/export-invoice";

    public static final String API_URL_RD_CANCEL_REPORT = "/api/report-data/cancel";

    public static final String API_URL_RD_EXPORT_REPORT = "/api/report-data/export";

    public static final String DOWN_BY_CLOUDID = "/file/downloadByCloudID";

    public static final String GETTRFBASEINFO = "/todoList/getTrfBaseInfo";

    public static final String TODO_List_Reason_List = "ToDoListRemoveReason";

    public static final String DBName = "todolist";


    public static final String SCI_COMMON_HEADER_REQUEST_ID = "requestId";
    public static final String SCI_COMMON_HEADER_LAB_CODE = "labCode";
    public static final String SCI_COMMON_HEADER_SYSTEM_ID = "systemId";
    public static final String ROOT_REPORT_NO = "rootReportNo";
    public static final String REPORT_NO = "reportNo";
    public static final String REVIEW_CONCLUSION = "reviewConclusion";
    public static final String CONCLUSION_REMARK = "conclusionRemark";
    public static final String COMMENTS = "comments";
    public static final String COUNTRY_NAME = "countryName";
    public static final String LOCATION_NAME = "locationName";
    public static final String ATTACHMENT_VALUE = "attachmentValue";
    public static final String SUPPLIER_NAME = "supplierName";


    public static final String CONFIG_STATUS_CONTROL = "TrfStatusControl";
    public static final String CONFIG_CUSTOMER_CONFIG = "CustomerConfig";
    public static final String CONFIG_SYSTEM_CONFIG = "SystemConfig";
    public static final String CONFIG_INVOICE_CURRENCY = "invoiceCurrency";

    public static final String CONFIG_INVOICE_RATE_TYPE = "invoiceRateType";
    public static final String TRF_PATTERN = "TrfPattern";
    public static final String USED_RD = "UsedRD";
    public static final String DELIVERY_REPORT_MODE = "DeliveryReportMode";
    public static final String ORIGIN_REPORT_MODE = "OriginReportDataMode";

    public static final String CONFIG_REPORT_DATA_SOURCE = "RdSource";

    public static final String SCI_NOTIFY_VERSION = "1.0";

    public static final String RENDER_TRF_DETAIL_TO_FILE = "/api/ReportAPI/GenerateReporting?appId={0}&reportingType={1}&isConvertToPdf={2}&token={3}";

    public static final String URL_PATTERN = "^(http|https)://.*$";

    public static final String EMPTY = "";

    public static final Integer ZERO = 0;

    public static final Integer NEGATIVE_ONE = -1;

    public static final String UNDER_LINE = "_";

    public static final Integer TWO = 2;

    public static final Integer ONE = 1;

    public static final Integer THREE = 3;
    public static final Integer FOUR = 4;

    //todo SCI-900
    public static final Integer SYSTEM_SGSMART=10002;

    public static final String USING_SGS_TRF_NO = "RefSystemIdSetOfUsingSGSTrfNo";

    public static final long LOWES_CUSTOMER_NO = 370196L;
    public static final String LOWES_NAME_OF_CUSTOMER_EN = "LG SOURCING INC";
    public static final String LOWES_NAME_OF_CUSTOMER_GROUP = "LOWE'S";

    public static final String LAB_CODE_OF_BASIC_FIELD_MAPPING = "labCode";

    public static final String $_SYSTEM_ID = "$.systemId";

}
